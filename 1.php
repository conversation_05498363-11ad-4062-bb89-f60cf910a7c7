<?php
/***********************************************************
 * 检测二维码登录
 * 
 * @date    2022-09-22
 * @version 2021-2023
 ***********************************************************/
use Swoole\Coroutine;
use Swoole\Coroutine\Http\Client;

// 设置初始化参数   
function init_redis(){
    return [
		"open" => 1,//是否转接ip 1 开启 2 关闭
		"ip" => "127.0.0.1:13670",
		"current_service" => 2, //当前服务器类型 1主服务器  2从服务器
		"current_service_address" => "http://************:81/setLocal.php", //从服务器需要配置同步主服务器地址
		"master_redis"=>[
			"host"=>'127.0.0.1',//配置redis地址
			"port"=>'6379', //端口
			"auth"=>'', //当前权限认证码
		],
		// 从服务器，current_service为2时需要配置
		"assistant_redis"=>[
			"host"=>'127.0.0.1',//配置redis地址
			"port"=>'6379', //端口
			"auth"=>'jeHYDphqIcV9VMNl', //当前权限认证码
		],
		"mysql"=>[
			'dbhost'    => '************',
			'port'		=> 3306,
			'dbname'    => 'xnqf_top',
			'username'  => 'xnqf_top',
			'pwd'       => 'FiEcbAy86GC6K2RS',
		],
		"redis_key"=>"xnqf_quqe_id2",
        "log_address"=>"/tmp/log/", //填写将使用当前配置目录，为空时默认是当前文件夹下存放日志
		// 重试次数
        "RETRY_COUNT"=>30, 
        // 等待时间
        "retry_all_1"=>1,
        "retry_all_2"=>2,
        "retry_all_3"=>3,
        "retry_all_4"=>4,
        "retry_all_5"=>5,
        "retry_all_6"=>6,
        "retry_all_10"=>10,
        "retry_all_15"=>20,
        "execute_type"=>[
            "只检测不删除模式",
            "检测并自动删除模式",
            "朋友圈屏蔽模式",
            "清理朋友圈",
            "删所有好友模式",
            "自动点赞",
            "清理收藏",
            "清理全部朋友圈",
            "自动评论",
            "自动点赞+评论",
            "固定步数",
            "随机步数",
            "立即修改固定步数"
        ]
    ];
}

// 初始化信息
$systemData = init_redis();
$redis = newRedis($systemData);
$res = init_Mysql($redis, $systemData);


// 数据赋值处理
$wxid = $res['wxid'];
$type = $res['type']; //0检测不删除1检测删除2朋友圈屏蔽3朋友圈清理4全部删除好友5点赞6收藏
$km = $res['kmcode'];
$uuid = $res['uuid'];
$gqtime = isset($res["gqtime"]) ? $res["gqtime"] : "";
$bs = isset($res["bs_context"]) ? $res["bs_context"] : "";
$bs2_context = isset($res["bs2_context"]) ? $res["bs2_context"] : 0;
$timing = isset($res["timing"]) ? $res["timing"] : 0;
$deleteNum = isset($res["delete_num"]) ? $res["delete_num"] : 0;
$maxid = isset($res["Maxid"]) ? $res["Maxid"] : 0;

// 是否配置原生ip
$proxy = $res['proxy'];
if($systemData["open"] == 1) $proxy = $systemData['ip'];
// 检测微信进程
if($wxid){
    // 记录用户已启动进程
    $redis->select(7);
    $redis->set($wxid, $type);
    $redis->close();

    // 检测进程是否存在
    $processList = [];
    $isProcess = 0;
    exec("pgrep -f {$wxid}", $processList, $isProcess);
    if ($isProcess === 0 && !empty($processList)) {
        exec("pkill -f {$wxid}");
    }
}else{
	ErrorMsg("为获取到用户微信id", $res);
	$redis->close();
	exit("异常任务 - 未检测到用户id");
}

ini_set("memory_limit", "350M");

cli_set_process_title($wxid);
$wxids = [];
$s_time = time();
$res["wxid"] = $wxid;
writeLog($proxy, ["start_time"=>$s_time,"res"=>$res], "启动任务，执行任务类型：".$systemData["execute_type"][$type]);
//发送文本消息
if ($gqtime && $type != 3 && $type != 5 && $type != 6 && $type!= 7  && $type!= 8 && $type!= 9 && $type!= 10 && $type!= 11 && $type!= 12) {
	if ($type == 0 && $timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n检测不删除模式！！！");
	if ($type == 1 && $timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n检测并自动删除模式！！！");
	if ($type == 2 && $timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n检测朋友圈屏蔽模式！！！");
	if ($type != 4 && $timing != 1) sendTextMsg($wxid, $proxy, "开始加载好友列表，请稍后...");
	//获取好友信息wxids
	$wxids = GetContractList($wxid, $proxy, $systemData);
	if (empty($wxids)) exit($wxid."  未取得好友数据wxids, 程序退出");
}

if ($type == 3 && $timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n清理朋友圈模式！！！");
if ($type == 7 && $timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n清理全部朋友圈模式！！！");

// type0检测不删除1检测删除2朋友圈屏蔽3朋友圈清理4全部删除好友
if ($type == 0 || $type == 1 || $type == 4) {
	$infos = [];
	$wxTickets = [];
	$blocks = [];
	$dels = [];
	if ($wxids && $type != 4) {
		sendTextMsg($wxid, $proxy, "好友列表加载完成！\n开始检测僵尸粉，根据好友数量\n时间不固定。请耐心等待");
		$ret = filter($wxids);
		unset($wxids);
		gc_collect_cycles();

		$infos = SwooleWXGetContactStatus($wxid, $proxy, $ret, $type);//返回array('blocks'=>[], 'dels'=>[]);
		$blocks = empty($infos['blocks']) ? [] : $infos['blocks'];//拉黑
		$dels = empty($infos['dels']) ? [] : $infos['dels'];//删除
		$infos = array_merge($dels, $blocks);
	}

	//检测不删除
	if ($type == "0") {
		if (!empty($infos)) {
			sendTextMsg($wxid, $proxy, "检测完成，开始推送僵尸粉……");
			$tsData = tuiSongXiaoxiGenMingpian($wxid, $proxy, $infos, 1, "阿拉丁[拉黑我的人]");
			unset($infos);
			gc_collect_cycles();
		}
		$laheiCount = $tsData['lh'] ?? 0;
		$shanchuCount = $tsData['sc'] ?? 0;
		if ($shanchuCount == 0 && $laheiCount == 0) {
			sendTextMsg($wxid, $proxy, "[检测完毕]\n[恭喜您，没有僵尸！！！]\n[您的人际关系很哇噻！]");
		} else {
			sendTextMsg($wxid, $proxy, "[检测完毕]\n[删除我的人]:".$shanchuCount. "\n[拉黑我的人]:".$laheiCount. "\n僵尸粉请查看【阿拉丁】标签。");
		}

		$e_time = time();
		$j_time = $e_time - $s_time;
		writeLog("", ['本次检测耗时'=>$j_time], "本次检测耗时{$j_time}秒");
		delRedis($wxid, $systemData);//删除运行标记
		exit("success");
	}

	if ($type == 1) {
		if (!empty($infos)) {
			sendTextMsg($wxid, $proxy, "检测完成，开始清理僵尸粉……");
			
			delFriend($wxid, $proxy, $infos, count($dels), count($blocks), $type);//删除好友
		}

		if (count($dels) == 0 && count($blocks) == 0) {
			sendTextMsg($wxid, $proxy, "[检测完毕]\n[恭喜您，没有僵尸！！！]\n[您的人际关系很哇噻！]");
		} else {
			sendTextMsg($wxid, $proxy, "[清理完毕]\n[删除我的人]:".count($dels). "\n[拉黑我的人]:".count($blocks). "\n[成功清理]:".count($infos)."\n[清理完成，自动停止]");
		}
		
		unset($infos);
		gc_collect_cycles();
		$e_time = time();
		$j_time = $e_time - $s_time;
		writeLog("", ['本次检测耗时'=>$j_time], "本次检测耗时{$j_time}秒");
		delRedis($wxid, $systemData);//删除运行标记
		exit("success");
	}
	
	if ($type == 4) {
		$ret = filter($wxids);
		unset($wxids);
		sendTextMsg($wxid, $proxy, "开始删除，请耐心等待");
		//删除全部好友
		delAllFriend($wxid, $proxy, $ret, $type, $gqtime, $systemData, $deleteNum);

		sendTextMsg($wxid, $proxy, "[清理完毕] \n[成功清理]:".count($ret)."\n[清理完成，自动停止]");
		delRedis($wxid, $systemData);//删除运行标记
		exit("success");
	}
}

if ($type == 2) {
	sendTextMsg($wxid, $proxy, "好友列表加载完成！\n开始检测朋友圈屏蔽，根据好友数量\n时间不固定。请耐心等待");
	$ret = filter($wxids);
	unset($wxids);
	 $res = WXSnsUserPage($wxid,$proxy, $ret);
	sendTextMsg($wxid,$proxy, "开始推送屏蔽我的人，请稍后");
	$jianceCount = 0;
	if (!empty($res)) {
		$jcData = tuiSongXiaoxiGenMingpian($wxid, $proxy, $res, 2, "阿拉丁[屏蔽我的人]");
		$jianceCount = $jcData['pb'];
	}

	sendTextMsg($wxid,$proxy, "[检测完毕]\n[屏蔽我的人]:".$jianceCount.  "\n剩余屏蔽我的人已添加到【阿拉丁】标签里。\n请自行查看，谢谢使用。");
	unset($res);
	gc_collect_cycles();
	$e_time = time();
	$j_time = $e_time - $s_time;
	writeLog("", ['本次检测耗时'=>$j_time], "本次检测耗时{$j_time}秒");
	delRedis($wxid, $systemData);//删除运行标记
	exit("success");
}

if ($type == 3  || $type == 7) {
	if ($type != 7) {
		$st = date("Y-m-d", $res['start_time']);
		$et = date("Y-m-d", $res['end_time']);
		sendTextMsg($wxid,$proxy, "开始清理\n{$st}-{$et}朋友圈\n请稍后...");
	} else {
		sendTextMsg($wxid,$proxy, "开始加载朋友圈，请稍后...");	
	}
	
	$t = FriendCircleDEL($wxid, $proxy, $res['start_time'], $res['end_time'], $type, $systemData, $maxid, $deleteNum);
	if ($t == 0 && $type == 3) {
		if ($res['start_time'] && $res['end_time'] ) {
			$st = date("Y-m-d", $res['start_time']);
			$et = date("Y-m-d", $res['end_time']);
			sendTextMsg($wxid, $proxy, "您选择的时间段\n{$st}-{$et}\n没有需要清理的内容\n请重新登录选择时间清理");
		} else {
			sendTextMsg($wxid, $proxy, "您选择的时间段\n没有需要清理的内容\n请重新登录选择时间清理");	
		}
		
	} else {
		sendTextMsg($wxid, $proxy, "[清理完毕]\n共清理{$t}条\n请自行检查否正确\n由于官方限制，若提示频繁\n请等待1小时后\n再重新登录清理");
	}
	gc_collect_cycles();
	delRedis($wxid, $systemData);//删除运行标记
	exit("success");
}
if ($type == 5) {
	if($timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁自动点赞】\n自动点赞已成功开启");
	
	//发送
	getLikeList($wxid, $proxy, $gqtime);
	exit("success");
}

if ($type == 6) {
	if($timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁-清理收藏】\n开始加载收藏列表，请稍后...");
	$data = getFavor($wxid, $proxy);

	$n = count($data);
	if($timing != 1) sendTextMsg($wxid, $proxy, "收藏加载完毕,开始删除");

	$dn = delFavor($wxid, $proxy, $data, $res['start_time'], $res['end_time']);

	sendTextMsg($wxid, $proxy, "[收藏清理完毕]\n[成功清理]:{$dn}\n请自行检查是否正确\n由于官方限制，若提示频繁\n请等待1小时后\n再重新登录清理");
	gc_collect_cycles();
	exit("success");
}

if ($type == 8) {
	//自动评论
	if($timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁自动评论】\n自动评论已成功开启");

	autoComments($wxid, $proxy, $gqtime);
	exit("success");
}

if ($type == 9) {
	// 评论+点赞
	if($timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁自动点赞+评论】\n自动点赞+评论已成功开启");

	autoLikeAndComments($wxid, $proxy, $gqtime);
	exit("success");
}

if ($type == 10) {
	// 修改步数
	if($timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n步数自动修改模式！！");
	
	if(!$bs){
	    $redis = newRedis($systemData);//初始化redis
	    $redis->select(4);
	    $bs = $redis->get($km."_bs");
	    $redis->close();
	}
	
	if($timing != 1) sendTextMsg($wxid, $proxy, "每日凌晨修改[{$bs}]步数!!");
	
	modifySteps($wxid, $proxy, $gqtime, $bs, $km, $type, $bs);
	delRedis($wxid, $systemData);//删除运行标记
	exit("success");
}
if ($type == 11) {
	// 修改步数
	if($timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n随机步数自动修改模式！！");
	
	if(!$bs2_context){
	    $redis = newRedis($systemData);//初始化redis
	    $redis->select(4);
	    $bs2_context = $redis->get($km."_bs2");
	    $redis->close();
	}
	
	if($timing != 1) sendTextMsg($wxid, $proxy, "每日凌晨随机修改[{$bs2_context}]步数!!");
	
	$bs2Arr = explode('-', $bs2_context);
    $bs = mt_rand($bs2Arr[0], $bs2Arr[1]);
	modifySteps($wxid, $proxy, $gqtime, $bs, $km, $type, $bs2_context);
	delRedis($wxid, $systemData);//删除运行标记
	exit("success");
}

//修改步数，不需要二次修改
if ($type == 12) {
	// 修改步数
	if($timing != 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n步数自动修改模式！！");
	
	if(!$bs){
	    $redis = newRedis($systemData);//初始化redis
	    $redis->select(4);
	    $bs = $redis->get($km."_bs");
	    $redis->close();
	}
	
	if($timing != 1) sendTextMsg($wxid, $proxy, "每日凌晨修改[{$bs}]步数!!");
	
	modifySteps($wxid, $proxy, $gqtime, $bs, $km, $type, $bs);
	delRedis($wxid, $systemData);//删除运行标记
	exit("success");
}

// modifySteps  设置步数
function modifySteps($wxid, $proxy, $gqtime, $bs, $codeName = "", $type= 10, $bs2_context= 1000) {
	if(($gqtime != 0 && time() >= $gqtime) || $bs <= 0){
	    writeLog("异常返回", ["wxid"=>$wxid,"proxy"=>$proxy,"codeName"=>$codeName,"gqtime"=>$gqtime,"bs2_context"=>$bs2_context],"验证码已过期");
	    exit("异常，验证码已过期");
	}
	$url = 'http://'.$proxy.'/api/Tools/UpdateStep';
        
	$params = array(
		"Number"=>intval($bs),
		"Wxid"=> "$wxid"
	);
    	
	$res = posturl($url, $params);
	
	if (isset($res['Code']) && $res['Code'] == 0) {
		sendTextMsg($wxid, $proxy, "今日步数[{$bs}]修改成功！");
		if($type == 12 ){
		    unset($res);
		    exit("修改成功");
		}
		// 休眠器：每天凌晨都会进行修改
		$nextMorningMidnight = strtotime("tomorrow", time());
		saveRedis($wxid, [
		    "type"=>$type,
		    "time"=>$nextMorningMidnight,
		    "gqtime"=>$gqtime,
		    "kmcode"=>$codeName,
		    "bs_context"=>$bs,
		    "bs2_context"=>$bs2_context,
		    "proxy"=>$proxy,
		    "wxid"=>$wxid
		]);
		writeLog($proxy, ["url"=>$url,"params"=>$params,"bs2_context"=>$bs2_context,"type"=>$type,"res"=>$res], "修改步数成功，启动休眠定时器");
		unset($res);
		exit("修改成功");
	}
	else if ($res['Code'] == '-13') {
		writeLog($proxy, $res, "修改失败，步数退出");
		exit('退出');
	}
}

// checkerm 检测登录
function checkerm($uuid, $proxy, $res, $redis, $systemData = [], $k= 0, $s=0) {
	if ($k == 100){
		ErrorMsg("当前用户超时未登录 : 100次", $res);
		return false;
	} 
    $url = 'http://'.$proxy.'/api/Login/CheckQR?uuid='.$uuid;
    $ret = posturl($url);

    // 检测用户是否有问题
    if(isWechatLoggedOut($ret)){
        writeLog("正常请求",["url"=>$url,'response'=>$ret],"用户登录失败：可能用户已退出终止任务");
        exit("可能用户已退出终止任务");
    }

    $status = empty($ret['Data']['status']) ? 0 : 1;
    if ($s == 0 && $status) {
    	$s = 1;
    	$res['headImgUrl'] = $ret['Data']['headImgUrl'];
		if($systemData["current_service"] == 2){
			// 同步机制
		 	posturl($systemData["current_service_address"], [
				'type'=>1, 
				'uuid'=>$res['uuid'], 
				'km_json'=>json_encode($res)
			]);
		}else{	
			$redis->select(12);
			$redis->set($res['uuid'], json_encode($res));
			$redis->expire($res['uuid'], 300);
		}
    }

	
	$users = !empty($ret['Data']['baseResponse']) && $ret['Data']['baseResponse']['ret'] == 0 ? $ret['Data']['acctSectResp'] : [];
	// 获取微信id
    $wxid = empty($users['userName']) ? '' : $users['userName'];
	
	// 取消掉代理
    if($wxid && $proxy){
        AgentOut($proxy, $wxid);
		unset($log);
    }

	$log = array(
		'msg' => '记录微信id:'.$k,
		'wxid'=> empty($users['userName']) ? '' : $users['userName'],
		'url' => $url,
		'data' => $ret
	);
	
	writeLog($url, $log, "用户扫码登录记录：" . $k);
	unset($log);

	if (empty($users)) {
		$k++;
		sleep(2);
		return checkerm($uuid, $proxy, $res, $redis, $systemData, $k, $s);
	}
	 
	return $users;
}

//isWechatLoggedOut 检测是否退出微信的函数
function isWechatLoggedOut($response) {
    // 检查 Message 字段
    if ($response['Message'] === '登陆异常') {
        // 检查 baseResponse 中的 ret 和 errMsg
        if (isset($response['Data']['baseResponse']['ret']) && $response['Data']['baseResponse']['ret'] === "-100") {
            $errMsg = $response['Data']['baseResponse']['errMsg']['string'];
            if (strpos($errMsg, '你已退出微信')!== false) {
                return true;
            }
        }
    }
	if ($response['Message'] === '数据不存在') return true;
	
    return false;
}

//checkCodeAndBind  检查卡密是否可以and绑定卡密
function checkCodeAndBind($wxid, $proxy, $res, $redis, $systemData=[])
{
	$values = $res['proxy']."@".$wxid;
    $redis->select(13);
    $redis->set($wxid, $values);
	$redis->select(12);
	if (strstr($res['kmcode'], '_kplum')) return true;
	$row = newMysql('select * from a_app_dailicode2 where code=\'' . $res['kmcode'] . '\'', 1);
	if (empty($row)) {
		sendTextMsg($wxid, $proxy, "未取得数据，程序终止。");
		writeLog($proxy,["codeName"=>$res['kmcode'],'wxid'=>$wxid],"未取得数据，程序终止。");
	
		$redis->close();
		ErrorMsg("检查绑定卡密: 未获取到数据", $row);
		exit("未取得数据，程序终止");
	}

	if (!empty($row['udid']) && $wxid != $row['udid']) {
		//不是绑定人等录
		$res['ismyself'] = 1;

		sendTextMsg($wxid, $proxy, "卡密已被使用，请购买新的卡密.");
		SynchronizeUserInfo($redis, $res, $systemData);
		writeLog($proxy,["codeName"=>$res['kmcode'],'wxid'=>$wxid,"row"=>$row],"卡密已被使用，请购买新的卡密.");
		
		$redis->close();
		ErrorMsg("检查绑定卡密: 卡密已被使用，请购买新的卡密", ['wxid'=>$wxid,"row"=>$row]);
        	exit("卡密已被使用，请购买新的卡密.");
    }

    //判断卡密是否过期
    if ($row['gqtime'] != 0 && time() >= $row['gqtime']) {
        //此激活码已过期;
		$res['iskmexp'] = 1;

		sendTextMsg($wxid, $proxy, "激活码过期，禁止使用。");
		SynchronizeUserInfo($redis, $res, $systemData);
		writeLog($proxy,["codeName"=>$res['kmcode'],'wxid'=>$wxid,"row"=>$row],"激活码过期，禁止使用。");
		
		$redis->close();
		ErrorMsg("检查绑定卡密: 激活码过期，禁止使用", ['wxid'=>$wxid,"row"=>$row]);
        	exit("激活码过期，禁止使用。");
    }

    if ($row['state'] == 2) {
    	//卡密封停终止程序
    	$res['isstate'] = 1;
		
		sendTextMsg($wxid, $proxy, "激活码冻结，禁止使用。");
		SynchronizeUserInfo($redis, $res, $systemData);
		writeLog($proxy,["codeName"=>$res['kmcode'],'wxid'=>$wxid,"row"=>$row],"激活码冻结，禁止使用。");
		
		$redis->close();
		ErrorMsg("激活码冻结，禁止使用。", ['wxid'=>$wxid,"row"=>$row]);
        	exit("激活码冻结，禁止使用。");

    }

    $gqtime = $row['gqtime'];
    if (empty($row['udid'])) {
    	$time = time();
        $gqtime = $row['tian'] * 86400 + $time;//过期时间
        $soletime = $time;//第一次使用时间
		newMysql('update a_app_dailicode2 set wssqauth=1,is_awaken=1, state=1,allstate=1,zijistate=1, soletime=' . $soletime . ',gqtime=' . $gqtime . ',udid=\''.$wxid.'\',ip=\''.$proxy.'\', diqu=\''.$res['pid'].'\', id_str=\''.$res['id_str'].'\' where id=\'' . $row['id'] . '\'', 2);
    } else {
        if (empty($row['id_str'])) {
			newMysql('update a_app_dailicode2 set is_awaken=1, ip=\''.$proxy.'\', diqu=\''.$res['pid'].'\', id_str=\''.$res['id_str'].'\' where id=\'' . $row['id'] . '\'', 2);
        } else {
			newMysql('update a_app_dailicode2 set is_awaken=1, ip=\''.$proxy.'\', diqu=\''.$res['pid'].'\' where id=\'' . $row['id'] . '\'', 2);
        }
    }

    if ($row['type'] == 8) {
    	$redis->select(4);
    	$t = $gqtime - time();
    	if ($res['bs_context'] != '') $redis->setex($res['kmcode']."_bs", $t, $res['bs_context']);
    	
    	if ($res['bs2_context'] != '') $redis->setex($res['kmcode']."_bs2", $t, $res['bs2_context']);
    	
    }
    unset($row);
    return $gqtime;
}

// SynchronizeUserInfo 同步用户基本信息
function SynchronizeUserInfo($redis, $res=[], $systemData = []){
	if($systemData["current_service"] == 2){
		// 同步机制
	 	posturl($systemData["current_service_address"], [
			'type'=>1, 
			'uuid'=>$res['uuid'], 
			'km_json'=>json_encode($res)
		]);
	}else{
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);
	}
}

//sendTextMsg 发送文本消息
function sendTextMsg($wxid, $proxy, $text)
{
	$url = 'http://'.$proxy.'/api/Msg/SendTxt';
	$params = array(
		"At"=> "",
		"Content"=> "$text",
		"ToWxid"=> "filehelper",
		"type"=>0,
		"Wxid"=> "$wxid"
	);
	sleep(1);
	$res = posturl($url, $params);
	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($wxid, $res, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep(3);
			posturl($url, $params);
		}
	}
	$log = array(
		'code' => isset($res['Code']) ? $res['Code'] : null,
		'Message' => isset($res) ? $res : null,
		'msg' => '发送文本消息返回',
		'sendText' => $text,
		'data'=> isset($res['data']) ? $res['data'] : null
	);
    writeLog($wxid, $log, "发送文本消息返回");
    unset($log);
    gc_collect_cycles();
}

// GetContractList 分页获取联系人
function GetContractList($wxid, $proxy, $systemData = [], $CurrentWxcontactSeq = 0, $wxids = [], $page = 1, $num=0)
{
	$url = 'http://'.$proxy.'/api/Friend/GetContractList';
	$params = array(
		'CurrentWxContactSeq' => $CurrentWxcontactSeq,
		'CurrentChatroomContactSeq' => 0,
		'Wxid' => $wxid
	);
	$data = posturl($url, $params);
	writeLog($proxy, ['data'=>$data, 'page'=>$page], "获取全部好友wxid");

	if (isset($data['Code'])&&$data['Code']!=0) {
		if ($data['Code'] == '-13') {
			writeLog($wxid, $data, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($data['Code'] == '-1' || $data['Code'] == '-8') {
			sleep($systemData["retry_all_3"]);
			$data = posturl($url, $params);
		}
	}


	if (empty($data['Data'])) {
	    writeLog($uuid, [], "获取好友WXIDS信息返回null 从新获取重。");
	    $num++;
	    if ($num < 5) {
	    	sleep($systemData["retry_all_1"]);
	    	return GetContractList($wxid, $proxy, $systemData, $CurrentWxcontactSeq, $wxids, $page, $num);
	    }
	    return $wxids;
	}

	if ($page % 5 == 0) {
		$text = sendTextMsg($wxid, $proxy, "正在加载第".$page."页");
	}

	if (!empty($data['Data']['ContactUsernameList'])) {

		$wxids = empty($wxids) ? $data['Data']['ContactUsernameList'] : array_merge($wxids, $data['Data']['ContactUsernameList']);
		if ($data['Data']['CurrentWxcontactSeq'] != $CurrentWxcontactSeq) {
			$CurrentWxcontactSeq = $data['Data']['CurrentWxcontactSeq'];
			$page += 1;
			sleep($systemData["retry_all_1"]);
			return GetContractList($wxid, $proxy, $systemData, $CurrentWxcontactSeq, $wxids, $page);
		}
	}
	unset($data);
	return $wxids;
}

// SwooleWXGetContactStatusTmp 并发检测用户
function SwooleWXGetContactStatus($wxid, $proxy, $friend_list, $type) 
{
	
	$results = [];
	$step = 2;
     Swoole\Coroutine\run(function () use($wxid, $proxy, $friend_list, $step, &$results) {
    	$total = count($friend_list);
    	if ($total == 0) return [];
	    $max_loop = ceil($total / $step);
	    $rate = 0;
	    $count = 0;
	    $percentage = 0;
	    $wg = new Coroutine\WaitGroup();
	    $channel = new Coroutine\Channel(1);
	    $percentageMutex = new Swoole\Lock(SWOOLE_MUTEX);
		for ($i = 0; $i < $max_loop; $i++) {
			$toWxids = array_splice($friend_list, 0, $step);
	        if ($toWxids == "") continue;
	        $wg->add();
	        try {
				Coroutine::create(function () use ($toWxids, $wxid, $proxy, $step, $total, &$results, $wg,&$rate,&$count,&$percentage, $percentageMutex,$channel, $i) {
					$res = SwooleGetFriendRelation($wxid, $toWxids, $proxy);
		            if (!empty($res)) {
		                if (!empty($res['dels'])) {
		                    $results['dels'] = empty($results['dels']) ? $res['dels'] : array_merge($results['dels'], $res['dels']);
		                }
		                if (!empty($res['blocks'])) {
		                    $results['blocks'] = empty($results['blocks']) ? $res['blocks'] : array_merge($results['blocks'], $res['blocks']);
		                }
		            }

		            $count++;
		            if (($i+1)%10 == 0) {//并发人数
		            	$rate = rate($wxid, $proxy, $count*$step, $total, $rate, "扫描进度");
		            	$channel->push(true);
		            }
		            unset($res);
		            $wg->done();
		            
				});
			} catch (Swoole\ExitException $e) {
			    // 处理协程退出异常
			    writeLog($wxid, ["e"=>$e], "检测到退出，停止程序");
			}
			if (($i+1)%10 == 0) $channel->pop();//并发人数
		}
		$wg->wait();
	});

    writeLog($proxy, $results, "好友数据");
    gc_collect_cycles();
	return $results;

}

// SwooleGetFriendRelation 查询用户关系 - 使用批量获取接口
function SwooleGetFriendRelation($wxid, $wxids, $proxy)
{
    $url = 'http://'.$proxy.'/api/Friend/GetContractDetail';
    $del = [];
    $block = [];

    // 批量处理，每次最多20个
    $totalArr = $wxids;
    $values = implode(',', $totalArr);

    $params = array(
        'ChatRoom' => '',
        'Towxids' => $values,
        'Wxid' => $wxid
    );

    sleep(1);
    $res = posturl($url, $params);
    writeLog($proxy, ['params'=>$params,'data'=>$res], "批量获取好友详情Swoole");

    if (isset($res['Code'])&&$res['Code']!=0) {
        if ($res['Code'] == '-13') {
            writeLog($wxid, $res, "检测到退出，停止程序");
            exit("检测到退出，停止程序");
        }
        if ($res['Code'] == '-1' || $res['Code'] == '-8') {
            sleep(3);
            $res = posturl($url, $params);
        }
    }

    if (!empty($res['Data'])) {
        if (!empty($res['Data']['Ticket'])) {
            foreach ($res['Data']['Ticket'] as $key => $value) {
                if (empty($value)) continue;
                $info = $res['Data']['ContactList'][$key];

                if (!empty($info['HasWeiXinHdHeadImg'])) {
                    // 有头像表示被删除
                    $del[] = [
                        'UserName' => ['string'=>$info['UserName']['string']],
                        'NickName' => ['string'=>$info['NickName']['string']],
                        'FriendRelation' => 1,
                    ];
                } else {
                    // 无头像表示被拉黑
                    $block[] = [
                        'UserName' => ['string'=>$info['UserName']['string']],
                        'NickName' => ['string'=>$info['NickName']['string']],
                        'FriendRelation' => 5,
                    ];
                }
            }
        }
    }

    return ['dels'=>$del, 'blocks'=>$block];
}

//filter 过滤公众号、群等
function filter($wxids)
{

	$ret = [];
	$noArr = ['weixin','qqmail','qqsafe','filehelper','mphelper','floatbottle','tmessage','medianote','lengtootoo','qmessage','fmessage','remembermaomao','vincentying','yingyongbao','cmb4008205555','wxid_6853388533711','qqwanggou001',"wxid_pzhf43hmwizd11", 'wxid_5768537685012', 'wxid_8887268872411', 'qqtech'];

	foreach ($wxids as $key => $value) {

		$m = explode("_", $value);
		$q = explode("@", $value);
		$q1 = isset($q[1]) ? $q[1] : '';
		if (in_array($value, $noArr) || $m[0] == 'gh' || $q1 == 'chatroom') continue;
		$ret[] = $value;
	}

	return $ret;
}

// rate 获取进度 -TODO 待验证
function rate($wxid, $proxy, $cr, $total, $rate, $text)
{
	if ($cr == 0) $cr = 1;
	$b = intval($cr / $total * 100);


	if ($b >= 5 && $b < 10 ) {
		// 10%;
		
		if ($rate != 5) {
			sendTextMsg($wxid, $proxy, $text."：5%");
			$rate = 5;
		}
	}
	if ($b >= 10 && $b < 15 ) {
		// 10%;
		
		if ($rate != 10) {
			sendTextMsg($wxid, $proxy, $text."：10%");
			$rate = 10;
		}
	}
	if ($b >= 15 && $b < 20 ) {
		// 10%;
		
		if ($rate != 15) {
			sendTextMsg($wxid, $proxy, $text."：15%");
			$rate = 15;
		}
	}
	if ($b >= 20 && $b < 25) {
		
		
		if ($rate != 20) {
			sendTextMsg($wxid, $proxy, $text."：20%");
			$rate = 20;
		}
	}
	if ($b >= 25 && $b < 30) {
		
		
		if ($rate != 25) {
			sendTextMsg($wxid, $proxy, $text."：25%");
			$rate = 25;
		}
	}
	if ($b >= 30 && $b < 35) {
		
		
		if ($rate != 30) {
			sendTextMsg($wxid, $proxy, $text."：30%");
			$rate = 30;	
		}
	}
	if ($b >= 35 && $b < 40) {
		
		
		if ($rate != 35) {
			sendTextMsg($wxid, $proxy, $text."：35%");
			$rate = 35;	
		}
	}
	if ($b >= 40 && $b < 45) {
		
		
		if ($rate != 40) {
			sendTextMsg($wxid, $proxy, $text."：40%");
			$rate = 40;
		}
	}
	if ($b >= 45 && $b < 50) {
		
		
		if ($rate != 45) {
			sendTextMsg($wxid, $proxy, $text."：45%");
			$rate = 45;
		}
	}
	if ($b >= 50 && $b < 55) {
		
		if ($rate != 50) {
			sendTextMsg($wxid, $proxy, $text."：50%");
			$rate = 50;
		}
	}
	if ($b >= 55 && $b < 60) {
		
		if ($rate != 55) {
			sendTextMsg($wxid, $proxy, $text."：55%");
			$rate = 55;
		}
	}
	if ($b >= 60 && $b < 65) {

		
		if ($rate != 60) {
			sendTextMsg($wxid, $proxy, $text."：60%");
			$rate = 60;
		}
	}
	if ($b >= 65 && $b < 70) {

		
		if ($rate != 65) {
			sendTextMsg($wxid, $proxy, $text."：65%");
			$rate = 65;
		}
	}
	if ($b >= 70 && $b < 75) {

		
		if ($rate != 70) {
			sendTextMsg($wxid, $proxy, $text."：70%");
			$rate = 70;
		}
	}
	if ($b >= 75 && $b < 80) {

		
		if ($rate != 75) {
			sendTextMsg($wxid, $proxy, $text."：75%");
			$rate = 75;
		}
	}
	if ($b >= 80 && $b < 85) {

		
		if ($rate != 80) {
			sendTextMsg($wxid, $proxy, $text."：80%");
			$rate = 80;
		}
	}
	if ($b >= 85 && $b < 90) {

		
		if ($rate != 85) {
			sendTextMsg($wxid, $proxy, $text."：85%");
			$rate = 85;
		}
	}
	if ($b >= 90 && $b < 95) {
		
		if ($rate != 90) {
			sendTextMsg($wxid, $proxy, $text."：90%");
			$rate = 90;
		}
	}
	if ($b >= 95 && $b < 99) {
		
		if ($rate != 95) {
			sendTextMsg($wxid, $proxy, $text."：95%");
			$rate = 95;
		}
	}
	if ($b >= 99 ) {
		if ($rate != 100) {
			$rate = 100;
			sendTextMsg($wxid, $proxy, $text."：100%");
		}
	}
	return $rate;
}

// tuiSongXiaoxiGenMingpian 检验用户
function tuiSongXiaoxiGenMingpian($wxid, $proxy, $data, $send = true, $title = "")
{
	$log = array(
		'Data' => $data,
		'msg' => '名片数据:tuiSongXiaoxiGenMingpian()',
	);
	writeLog($proxy, $log, "");
	unset($log);
	$sendText = "";
	$delId = 0;
	$blockId = 0;
	$pdId = 0;
	$jianceCount = 0;
	$f = 0;
	$laheiCount = 0;
	$shanchuCount = 0;
	$pingbiCount = 0;
	foreach ($data as $k => $value) {
		if (!isset($value['UserName']['string'])) continue;
		if (!isset($value['NickName']['string'])) continue;
		
		if ($send == "1") {
			//添加标签
			if ($value['FriendRelation'] == 1) {
				if ($delId == 0) {
					$delId = createBiaoqian($wxid, $proxy, "阿拉丁-删除我的人");
				}
				$res = addBiaoqianWxids($wxid, $proxy, $value, $delId);
				if (!empty($res) && isset($res['Data']['BaseResponse']['ret']) && $res['Data']['BaseResponse']['ret'] != 0) continue;
				$shanchuCount++;
				$jianceCount++;
			} elseif($value['FriendRelation'] == 5) {
				if ($blockId == 0) {
					$blockId = createBiaoqian($wxid, $proxy, "阿拉丁-拉黑我的人");
				}
				$res = addBiaoqianWxids($wxid, $proxy, $value, $blockId);
				if (!empty($res) && isset($res['Data']['BaseResponse']['ret']) && $res['Data']['BaseResponse']['ret'] != 0) continue;
				$laheiCount++;
				$jianceCount++;
			}
		}
		if ($send == "2") {
			if ($pdId == 0) {
					$pdId = createBiaoqian($wxid, $proxy, "阿拉丁-屏蔽我的人");
				}
				$res = addBiaoqianWxids($wxid, $proxy, $value, $pdId);
				if (!empty($res) && isset($res['Data']['BaseResponse']['ret']) && $res['Data']['BaseResponse']['ret'] != 0) continue;
				$pingbiCount++;
				$jianceCount++;
		}
		if ($jianceCount == 11) sendTextMsg($wxid, $proxy, "名片推送有限制，请到通讯录标签里查看");
		if ($jianceCount <= 10) {
			sendZhenshiMingpian($wxid, $proxy, $value);
		} else {
			$f++;
			if ($send == "1") {
				if ($value['FriendRelation'] == 1) {
					$sendText = empty($sendText) ? "阿拉丁-[删除我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-[删除我的人]-".$value['NickName']['string'];
				} elseif($value['FriendRelation'] == 5) {
					$sendText = empty($sendText) ? "阿拉丁-[拉黑我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-[拉黑我的人]-".$value['NickName']['string'];
				}
			}
			if ($send == "2") {
				$sendText = empty($sendText) ? "阿拉丁-[屏蔽我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-[屏蔽我的人]-".$value['NickName']['string'];
			}
			
		}

		if ($f != 0 && $f % 10 == 0) {
			sendTextMsg($wxid, $proxy, $sendText);
			$sendText = "";
		}
	}

	if (!empty($sendText)) {
		sendTextMsg($wxid, $proxy, $sendText);
		$sendText = "";
	}
	unset($res);
	return ['pb'=>$pingbiCount, 'lh'=>$laheiCount, 'sc'=>$shanchuCount];

}

//sendZhenshiMingpian 发送真实名片
function sendZhenshiMingpian($wxid, $proxy, $info)
{
	$url = 'http://'.$proxy.'/api/Msg/ShareCard';
	$params = array(
			"CardAlias" => $info['Alias']??'',
			'CardNickName' => $info['NickName']['string'],
			'CardWxId' => $info['UserName']['string'],
			'ToWxid' => "filehelper",
			'Wxid' => $wxid
		);
	sleep(2.5);
	$res = posturl($url, $params);

	$log = array(
		'Message' => isset($res['Message']) ? $res['Message'] : null,
		'msg' => '获取发送名片返回:sendZhenshiMingpian()',
		'data'=> $res
	);
	writeLog($proxy, $log, "");
	unset($log);
}

// createBiaoqian 新建标签
function createBiaoqian($wxid, $proxy, $name)
{
	
	$url = 'http://'.$proxy.'/api/Label/Add';
	$res = posturl($url, array('LabelName'=>$name, 'Wxid'=>$wxid));
	$labelId = 0;
	if ($res['Code'] == 0 && !empty($res['Data'])) {
		$labelId = $res['Data']['LabelPairList']['labelID'] ?? 0;
	}
	$log = array(
		'Message' => $res['Message'] ?? '',
		'msg' => '创建标签:createBiaoqian()',
		'data'=>$res
	);
	writeLog($proxy, $log, "");
	unset($log);

	return $labelId;
    
    if (empty($res['data']['LabelPairList'][0]['LabelID'])) {
    	return createBiaoqian($guid, $name);
    }
}

// addBiaoqianWxids 添加僵尸粉到标签
function addBiaoqianWxids($wxid, $proxy, $info, $bid)
{

	$url = 'http://'.$proxy.'/api/Label/UpdateList';
	if (!isset($info['UserName']['string'])) return;
	$toWxid = $info['UserName']['string'];
	$params = ['ToWxids'=>$toWxid, 'LabelID'=> "$bid", 'Wxid'=>$wxid];
	$res = posturl($url, $params);
	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($wxid, $res, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep(3);
			$res = posturl($url, $params);
		}
	}
	if (!empty($res) && isset($res['Data']['BaseResponse']['ret']) && $res['Data']['BaseResponse']['ret'] != 0) {
		writeLog($wxid, $res, "添加僵尸粉到标签---");
		if(!is_dir('/tmp/log/biaoqianlog/')) mkdir("/tmp/log/biaoqianlog/",0755,true);
		file_put_contents("/tmp/log/biaoqianlog/".$wxid.".log", date("Y/m/d H:i:s")."添加僵尸粉到标签---".var_export($res,true)."\n\n", FILE_APPEND);
		sleep(3);
		$res = posturl($url, $params);
	}
	$log = array(
		'Message' => isset($res['MoveToLabel']) ? $res['MoveToLabel'] : null,
		'params' => $params,
		'data'=>$res,
		'msg' => '添加僵尸粉到标签:addBiaoqianWxids()',
	);
	writeLog($proxy, $log, "");
	unset($log);
	return $res;
}

// delFriend 删除好友
function delFriend($wxid, $proxy, $data, $delNum, $blockNum, $type=1)
{
	// 先推送几个
	$delUrl = 'http://'.$proxy.'/api/Friend/Delete';
	$sendText = "";
	foreach ($data as $key => $value) {
		if (empty($value)) continue;
		if (!isset($value['NickName']['string'])) continue;
		$toWxid = $value['UserName']['string'];
		//执行删除操作
		sleep(1);
		$delParams = array(
			'ToWxid' => "$toWxid",
			'Wxid' => $wxid,
		);
		$delRes = posturl($delUrl, $delParams);

		if (!empty($delRes) && isset($delRes['Data']['oplogRet']['errMsg'])) {
			sendTextMsg($wxid, $proxy, $sendText);
			$sendText = "";
			$qingli = $key;
			$weiqingli = count($data)-$key;
			if ($qingli == 0) {
				sendTextMsg($wxid, $proxy, "[成功清理]:".$qingli."（删除频繁）\n[剩余未清理]:".$weiqingli."（删除频繁）\n请自行检查清理人数是否正确\n由于官方限制，若提示频繁\n默认1小时后自动清理剩余僵尸\n或1小时后再重新登录清理");
			} else {
				sendTextMsg($wxid, $proxy, "[成功清理]:".$qingli."\n[剩余未清理]:".$weiqingli."（删除频繁）\n请自行检查清理人数是否正确\n由于官方限制，若提示频繁\n默认1小时后自动清理剩余僵尸\n或隔天再重新登录清理");
			}
			
			if(!is_dir('/tmp/log/shanchu/')) mkdir("/tmp/log/shanchu/",0755,true);
			file_put_contents("/tmp/log/shanchu/".$GLOBALS['km'].".log", date("Y/m/d H:i:s")."删除频繁：删除人数：".$qingli.json_encode(['data'=>$delRes,'proxy'=>$proxy,'wxid'=>$wxid], JSON_UNESCAPED_UNICODE)."\n\n", FILE_APPEND);
			
			// 休眠器：运行超时1小时后再试
			$startTime = time() + 3600;
			saveRedis($wxid, [
				"type"=>$type,
				"time"=>$startTime,
				"proxy"=>$proxy,
				"wxid"=>$wxid,
				"kmcode"=>$GLOBALS['km']
			]);
			writeLog($wxid, ['data'=>$delRes,'params'=>$delParams], "删除频繁");
			unset($delRes);
            exit("异常退出-删除僵尸粉频繁-启动定时删除");
		}

		$log = array(
			'Message' => isset($delRes['Message']) ? $delRes['Message'] : null,
			'msg' => '删除1好友:delFriend()',
			// 'data'=>$delRes
		);
		writeLog($wxid, $log, "");
		unset($log);
		if ($value['FriendRelation'] == 1) {
			$sendText = empty($sendText) ? "阿拉丁-已清理[删除我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-已清理[删除我的人]-".$value['NickName']['string'];
		} else if ($value['FriendRelation'] == 5){
			$sendText = empty($sendText) ? "阿拉丁-已清理[拉黑我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-已清理[拉黑我的人]-".$value['NickName']['string'];
		}
		if (($key+1) % 10 == 0)  {
			sendTextMsg($wxid, $proxy, $sendText);
			$sendText = "";
		}
		unset($toWxid);
		unset($delRes);
	}
	if (!empty($sendText)) sendTextMsg($wxid, $proxy, $sendText);
	unset($data);
	unset($sendText);

	gc_collect_cycles();
}

// delAllFriend 删除全部好友
function delAllFriend($wxid, $proxy, $data, $type =4, $gqtime=0, $systemData=[], $num=0)
{
	// 先推送几个
	$delUrl = 'http://'.$proxy.'/api/Friend/Delete';
	$sendText = "";
	foreach ($data as $key => $value) {
		if($num != 0) $key = $num + $key;
		if (empty($value)) continue;
		$toWxid = $value;
		//执行删除操作
		sleep($systemData["retry_all_1"]);
		$delParams = array(
			'ToWxid' => "$toWxid",
			'Wxid' => $wxid,
		);
		$delRes = posturl($delUrl, $delParams);
		
		$log = array(
			'msg' => '删除好友:delAllFriend()',
			'params' => $delParams,
			'data'=> $delRes
		);
		writeLog($wxid, $log, "删除好友:delAllFriend()");
		unset($log);

		if (!empty($delRes) && isset($delRes['Data']['oplogRet']['errMsg'])) {
			$qingli = $key;
			$weiqingli = count($data)-$key;
			
			if ($qingli == 0) {
				sendTextMsg($wxid, $proxy, "[成功清理]:".$qingli."（删除频繁）\n[剩余未清理]:".$weiqingli."（删除频繁）\n请自行检查清理人数是否正确\n由于官方限制，若提示频繁\n默认1小时后自动清理剩余僵尸\n或1小时后再重新登录清理");
			} else {
				sendTextMsg($wxid, $proxy, "[成功清理]:".$qingli."\n[剩余未清理]:".$weiqingli."（删除频繁）\n请自行检查清理人数是否正确\n由于官方限制，若提示频繁\n默认1小时后自动清理剩余僵尸\n或1小时后再重新登录清理");
			}
			
			if(!is_dir('/tmp/log/shanchu/')) mkdir("/tmp/log/shanchu/",0755,true);
			file_put_contents("/tmp/log/shanchu/".$GLOBALS['km'].".log", date("Y/m/d H:i:s")."删除频繁：删除人数：".$qingli.json_encode($delRes, JSON_UNESCAPED_UNICODE)."\n\n", FILE_APPEND);

			$newTimestamp =  time() + 3600;
			saveRedis($wxid, [
				"type"=>$type,
				"time"=>$newTimestamp,
				"proxy"=>$proxy,
				"gqtime"=>$gqtime,
				"wxid"=>$wxid,
				"delete_num" => $key,
				"kmcode"=>$GLOBALS['km']
			]);
			writeLog($wxid, ['data'=>$delRes,'params'=>$delParams], "删除频繁");
			unset($delRes);
			exit("异常退出:删除全部好友频繁");
		}

		if ($key == 30) sendTextMsg($wxid, $proxy, "清理进度：已删除人数31；");
		if ($key == 60) sendTextMsg($wxid, $proxy, "清理进度：已删除人数61；");
		if ($key == 90) sendTextMsg($wxid, $proxy, "清理进度：已删除人数91；");
		if ($key == 120) sendTextMsg($wxid, $proxy, "清理进度：已删除人数121；");
		if ($key == 150) sendTextMsg($wxid, $proxy, "清理进度：已删除人数151；");
		if ($key == 180) sendTextMsg($wxid, $proxy, "清理进度：已删除人数181；");
		if ($key == 210) sendTextMsg($wxid, $proxy, "清理进度：已删除人数211；");
		if ($key == 240) sendTextMsg($wxid, $proxy, "清理进度：已删除人数241；");
		if ($key == 270) sendTextMsg($wxid, $proxy, "清理进度：已删除人数271；");
		if ($key == 298) sendTextMsg($wxid, $proxy, "清理进度：已删除人数300；");
	}
	unset($data);
}

// WXSnsUserPage 获取好友朋友圈
function WXSnsUserPage($wxid, $proxy, $friend_list, $step=1)
{
	writeLog($proxy, $friend_list, "全部好友id");
	$results = [];
    Swoole\Coroutine\run(function () use($wxid, $proxy, $friend_list, $step, &$results) {
    	$total = count($friend_list);
	    $max_loop = ceil($total / $step);
	    $rate = 0;
	    $count = 0;
	    $percentage = 0;
	    $wg = new Coroutine\WaitGroup();
	    $channel = new Coroutine\Channel(1);
		for ($i = 0; $i < $max_loop; $i++) {
			$values = array_splice($friend_list, 0, $step);
	        $wxidStr = implode(',', $values);
	        if ($wxidStr == "") continue;
	        $wg->add();
	        try {
				Coroutine::create(function () use ($wxidStr, $wxid, $proxy, $step, $total, &$results, $wg,&$rate,&$count,&$percentage, $channel,$i) {
					$res = SwooleWXSnsUserPage($wxid, $wxidStr, $proxy);
		            if (!empty($res)) {
	                    $res1 = SwoolegetFriendDetail2($wxid, $res, $proxy);
	                    if (!empty($res1)) {
	                    	$results = empty($results) ? $res1 : array_merge($results, $res1);
	                    }
	                }

		            $count++;
		            if (($i+1) % 10 == 0) {//屏蔽并发人数
		            	$rate = rate($wxid, $proxy, $count*$step, $total, $rate, "扫描进度");	
		            	$channel->push(true);
		            }
		            
		            $wg->done();
		            
				});
				if (($i+1) % 10 == 0) $channel->pop();
			} catch (Swoole\ExitException $e) {
			    // 处理协程退出异常
			    writeLog($wxid, [], "检测到退出，停止程序");
			}
		}
		$wg->wait();
	});
	writeLog($proxy, $results, "屏蔽好友数据");
	return $results;
}

// SwooleWXSnsUserPage 获取朋友圈屏蔽
function SwooleWXSnsUserPage($wxid, $wxidStr, $proxy)
{
    $allInfos = [];
    $url = 'http://'.$proxy.'/api/FriendCircle/GetDetail';
    $wxidData = explode(',', $wxidStr);
    $ac = count($wxidData);
    foreach ($wxidData as $key => $toWxid) {
        $params = array(
            'Towxid' => $toWxid,
            'FirstPageMd5' => "",
            'Maxid' => 0,
            "Wxid" => $wxid
        );
        sleep(1);
        $res = posturl($url, $params);
        writeLog($proxy, ['params'=>$params,'data'=>$res], "朋友圈屏蔽Swoole");

        if (!isset($res['Data']['ObjectTotalCount'])) continue;

        if ($res['Data']['ObjectTotalCount'][0] == "0") {

            $allInfos[] = $toWxid;
        }
    }
    return $allInfos;

}

// SwoolegetFriendDetail2  获取朋友圈屏蔽2
function SwoolegetFriendDetail2($wxid, $wxids, $proxy) 
{
    $allInfos = [];
    $url = 'http://'.$proxy.'/api/Friend/GetContractDetail';
    foreach ($wxids as $key => $toWxid) {

        $params = array(
            'ChatRoom' => '',
            'Towxids' => $toWxid,
            'Wxid' => $wxid
        );
        sleep(1);
        $res = posturl($url, $params);

        $result = isset($res['Data']['ContactList']) ? $res['Data']['ContactList'][0] : [];
        $allInfos[] = $result;
    }
    return $allInfos;
}

// FriendCircleDEL 获取本人朋友圈
function FriendCircleDEL_test($wxid, $proxy, $start, $end, $type, $systemData=[], $Maxid = 0, $k = 0, $cur=0, $cr=0) {
	$url = 'http://'.$proxy.'/api/FriendCircle/GetDetail';
	$params = array(
		'Towxid' => $wxid,
		'FirstPageMd5' => "",
		'Maxid' => 131230982,
		"Wxid" => $wxid
	);
	$res = posturldel($url, $params, $Maxid);
	sleep($systemData["retry_all_1"]);
	$startStr = empty($start) ? "" : '开始时间'.date("Y-m-d H:i:s", $start);
	$endStr = empty($end) ? "" : '开始时间'.date("Y-m-d H:i:s", $end);
	if (isset($res['Code']) && $res['Code'] != '0') {
		$log = array(
				'code' => isset($res['Code']) ? $res['Code'] : null,
				'Message' => isset($res['Message']) ? $res['Message'] : null,
				'msg' => '获取朋友圈:FriendCircleDEL()',
				'start'=> $startStr,
				'end'=> $endStr,
				'data' => isset($res['Data']) ? $res['Data'] : null
			);
		writeLog($wxid, $log, "获取朋友圈:WXSnsUserPage()");
		unset($log);
	}

	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($wxid, $res, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep($systemData["retry_all_3"]);
			$res = posturldel($url, $params, $Maxid);
			writeLog($url, $res, "二次获取朋友圈");
		}
	}

	if (!empty($res) && $res['Code'] == 0 && !empty($res['Data']['ObjectList'])) {
		$data = $res['Data']['ObjectList'] ?? [];
		foreach ($data as $key => $value) {
			$id = $value['Id'];
			if ($type == 3) {
				if ($start > $value['CreateTime']) {
					unset($res);
					return $k;
				}

				if ($start < $value['CreateTime'] && $value['CreateTime'] < $end) {
					$cur = 99999;
					$xid = "";
					$k += 1;
					delFriendCircle($wxid, $proxy, $id, $k, $value['CreateTime'], $type);
				}
			} else if ($type == 7) {
				$k += 1;
				delFriendCircle($wxid, $proxy, $id, $k, $value['CreateTime'], $type);
			}
		}
		if ($cur != 99999) {
			$cur++;
			if ($cur%20 == 0) sendTextMsg($wxid, $proxy, "加载页数：".$cur);
		}
		unset($res);
		return FriendCircleDEL($wxid, $proxy, $start, $end, $type, $systemData, $id, $k, $cur);
	}else{
	    $logMassage = [
	        "code"=>"GetDetail",
	        "url"=>$url,
	        "params"=>$params,
	        "return_value"=>$res,
	    ];
    
	    userLog("当前用户检测朋友圈失败+++++++++++", $logMassage, "/tmp/swoole/log/yang.log");
	}
	unset($res);
	return $k;
}

function FriendCircleDEL($wxid, $proxy, $start, $end, $type, $systemData = [], $Maxid = 0, $k = 0, $cur = 0, $cr = 0) {
	$url = 'http://' . $proxy . '/api/FriendCircle/GetDetail';
	$processedCount = $k; // 已处理的朋友圈数量
	$pageCount = $cur;    // 当前页数
	
	$params = [
		'Towxid' => $wxid,
		'FirstPageMd5' => '',
		'Maxid' => 131230982,  // 使用当前Maxid
		'Wxid' => $wxid
	];
	// 循环处理所有页面，直到没有更多数据
	while (true) {
		// 赋值信息
		//$params["Maxid"] = ;
		// 发送请求并获取响应
		$res = posturldel($url, $params, $Maxid);
		sleep($systemData["retry_all_1"] ?? 1); // 默认等待1秒
		
		// 处理请求错误
		if (isset($res['Code']) && $res['Code'] != '0') {
			// 记录开始和结束时间
			$log = [
				'code' => $res['Code'] ?? null,
				'Message' => $res['Message'] ?? null,
				'msg' => '获取朋友圈:FriendCircleDEL()',
				'start' => empty($start) ? '' : '开始时间' . date("Y-m-d H:i:s", $start),
				'end' => empty($end) ? '' : '结束时间' . date("Y-m-d H:i:s", $end),
				'data' => $res['Data'] ?? null,
				'params' =>$params,
				'Maxid' =>$Maxid,
			];
			writeLog($wxid, $log, "获取朋友圈:WXSnsUserPage()");
			unset($log);
			// 特殊错误处理
			if ($res['Code'] == '-13') {
				writeLog($wxid, $res, "检测到退出，停止程序");
				return $processedCount; // 返回已处理数量而非exit
			}
			
			if (in_array($res['Code'], ['-1', '-8'])) {
				sleep($systemData["retry_all_3"] ?? 3); // 默认等待3秒
				$res = posturldel($url, $params, $Maxid); // 重试请求
				writeLog($url, ["Maxid"=>$Maxid,"res"=>$res], "二次获取朋友圈");
			}
		}
		
		// 检查是否有有效数据
		if (empty($res) || $res['Code'] != 0 || empty($res['Data']['ObjectList'])) {
			$logMessage = [
				"code" => "GetDetail",
				"url" => $url,
				"params" => $params,
				"Maxid" => $Maxid,
				"return_value" => $res,
			];
			userLog("当前用户检测朋友圈失败+++++++++++", $logMessage, __DIR__ . "/yang.log");
			unset($logMessage);
			break; // 退出循环
		}
		
		// 处理朋友圈数据
		$data = $res['Data']['ObjectList'] ?? [];
		$lastItemId = 0; // 记录最后一条数据的ID，用于翻页
		
		if (!empty($data)){
    		foreach ($data as $key => $value) {
    			$id = $value['Id'];
    			$createTime = $value['CreateTime'];
    			$lastItemId = $id; // 更新最后一条ID
    			// 根据类型筛选
    			if ($type == 3) {
    				// 如果内容创建时间早于开始时间，停止处理
    				if ($start > $createTime) {
    					return $processedCount;
    				}
    				
    				// 如果在时间范围内，删除朋友圈
    				if ($start < $createTime && $createTime < $end) {
    					$processedCount++;
    					delFriendCircle($wxid, $proxy, $id, $processedCount, $createTime, $type);
    				}
    			} elseif ($type == 7) {
    				// 类型7直接删除所有
    				$processedCount++;
    				delFriendCircle($wxid, $proxy, $id, $processedCount, $createTime, $type);
    			}
    		}
		}
		// 更新页数并发送进度通知
		$pageCount++;
		if ($processedCount != 0 && $pageCount % 20 == 0) {
			sendTextMsg($wxid, $proxy, "加载页数：{$pageCount}，已处理：{$processedCount}条");
		}
		
		// 如果没有更多数据，退出循环
		if (empty($lastItemId)) {
			break;
		}
		
		// 更新Maxid，准备获取下一页
		$Maxid = $lastItemId;
	}
	return $processedCount;
}    

function userLog($title="", $data=[], $fileAddress=""){
      	// 获取当前时间
	$time = date('Y-m-d H:i:s');
	// 创建日志条目的基础信息
	$logArr = "{$time} {$title}";
	// 添加参数（如果有的话）
	if (!empty($data)) {
	    $logArr .= ' ' . json_encode($data,JSON_UNESCAPED_UNICODE);
	}
	// 添加换行符
	$logArr .= PHP_EOL;


    file_put_contents($fileAddress, $logArr, FILE_APPEND); 
}


// delFriendCircle 删除朋友圈
function delFriendCircle($wxid, $proxy, $id, $k = 0, $createTime=0, $type=1) {
	$url = 'http://'.$proxy.'/api/FriendCircle/Operation';
	$params = array(
		'CommnetId' => 0,
		'Id' => $id,
		'Type' => 1,
		"Wxid" => $wxid
	);
	sleep(1);
	$res = posturl($url, $params);
	writeLog($proxy, ['data'=>$res,'delId'=>$id,"createTime"=>$createTime,'params'=>$params], "清理朋友圈结果返回");

	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($wxid, $res, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep(3);
			$res = posturl($url, $params);
			writeLog($proxy, $res, "二次删除朋友圈结果");
		}
	}

	if (isset($res['Data']['BaseResponse']) && $res['Data']['BaseResponse']['ret'] != '0') {
		sendTextMsg($wxid, $proxy, "[清理频繁]\n由于官方限制，提示频繁\n默认凌晨自动清理\n或隔天再重新登录清理");

		if(!is_dir('/tmp/log/pengyouquan/')) mkdir("/tmp/log/pengyouquan/",0777,true);
		file_put_contents("/tmp/log/pengyouquan/{$wxid}.log", date("Y/m/d H:i:s")."获取朋友圈：".json_encode(['p_切片'=>$k, 'data-切片'=>$res, 'code-切片'=>$GLOBALS['km']], JSON_UNESCAPED_UNICODE)."\n\n", FILE_APPEND);


		$newTimestamp = strtotime(date('Y-m-d', time() + 86400)." 00:10:00");
		saveRedis($wxid, [
			"type"=>$type,
			"time"=>$newTimestamp,
			"proxy"=>$proxy,
			"start_time"=>$start,
			"end_time"=>$end,
			"wxid"=>$wxid,
			"Maxid"=>$id,
			"delete_num"=>$k,
			"kmcode"=>$GLOBALS['km'],
		]);
		writeLog($url,["url"=>$url,"params"=>$params,"response"=>$res],"[清理频繁]\n由于官方限制，提示频繁\n默认凌晨自动清理\n或隔天再重新登录清理");
		unset($res);
		exit("异常退出-删除朋友圈频繁-启动定时器");
	}
	// if ($k % 5 == 0) sendTextMsg($wxid, $proxy, "已删除".$k."条");
	unset($res);
	if ($k <= 10) {
		sendTextMsg($wxid, $proxy, "已删除".$k."条");
		return;	
	} if ($k > 10 && $k <=100) {
		if ($k % 5 == 0) sendTextMsg($wxid, $proxy, "已删除".$k."条");
		return;
	} else {
		if ($k % 10 == 0) sendTextMsg($wxid, $proxy, "已删除".$k."条");
		return;
	}
}

// writeLog 创建日志信息
function writeLog($guid, $data, $title){
	// 获取当前时间
	$time = date('Y-m-d H:i:s');
	// 创建日志条目的基础信息
	$logArr = "{$time} {$title} {$guid}";
	// 添加参数（如果有的话）
	if (!empty($data)) {
	    $logArr .= ' ' . json_encode($data,JSON_UNESCAPED_UNICODE);
	}
	// 添加换行符
	$logArr .= PHP_EOL;

	// 存放日志地址
	$fileAddress =  $GLOBALS["fileNameAddress"];
    file_put_contents($fileAddress, $logArr, FILE_APPEND);
    unset($data);
    unset($guid);
    unset($title);
	unset($logArr);
	unset($fileAddress);
}

// TwiceAutoAuth 二次唤醒登录
function TwiceAutoAuth($url, $wxid) {
	$parsedUrl = parse_url($url);
	$host = $parsedUrl['host'].":".$parsedUrl['port'];
	$url = 'http://'.$host.'/api/Login/TwiceAutoAuth?wxid='.$wxid;
	$res = posturl($url, ['Wxid'=>$wxid]);
	
	
	if (isset($res['Code']) && $res['Code'] == '0') {
		if(!is_dir('/tmp/log/erci/')) mkdir("/tmp/log/erci/",0755,true);
		file_put_contents("/tmp/log/erci/{$wxid}.log", date("Y/m/d H:i:s")."二次登录成功返回"  .var_export(['er'=>$res, 'km'=>$GLOBALS['km']], true)."\n\n", FILE_APPEND);
	} else {
		if (isset($res['Code']) && $res['Code'] != '1' && strpos($res['Message'], "登录失败") === false) {
			if(!is_dir('/tmp/log/erci/')) mkdir("/tmp/log/erci/",0755,true);
			file_put_contents("/tmp/log/erci/{$wxid}.log", date("Y/m/d H:i:s")."二次登录返回"  .var_export(['er'=>$res, 'par'=>$data, 'km'=>$GLOBALS['km']], true)."\n\n", FILE_APPEND);
		}
	}
	
	
	
	writeLog($url,["url"=>$url,"Wxid"=>$wxid,"response"=>$res],"二次登录请求");
	if (isset($res['Code']) && ($res['Code'] == 0  || $res['Code'] == '0' || strpos($res['Message'], "登陆成功") != false)) {
        writeLog($url, ["url"=>$url,'response'=>$res],"二次登录成功返回”");
	    return $res;
    } 
	
	writeLog($url, ["url"=>$url,'response'=>$res],"二次登录失败，用户已退出");
	exit("异常返回-二次登录用户已退出");
}

//AgentOut 清理登录代理
function AgentOut($proxy, $wxid) {
    if($wxid){
       $url = 'http://'. $proxy .'/api/Tools/setproxy';
       $res = posturl($url, ['Wxid'=>$wxid]);
	   writeLog($url, ["url"=>$url,'response'=>$res],"微信用户清理代理");
	   unset($res);
    }
}

// posturl  post请求
function posturl($url,$postdata = [], $mix=0){
	$wxid = $postdata['Wxid'] ?? "init_wxid";
	$data = $postdata;
    $postdata = json_encode($postdata);
    # 初始化一个curl会话
    $ch = curl_init();
	# 启用时会发送一个常规的POST请求
    curl_setopt($ch, CURLOPT_POST, 1);
	# 需要获取的URL地址
    curl_setopt($ch, CURLOPT_URL, $url);
	# 全部数据使用HTTP协议中的"POST"操作来发送。
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
	# 将curl_exec()获取的信息以文件流的形式返回，而不是直接输出。
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT,30);   //只需要设置一个秒的数量就可以 
	# 一个用来设置HTTP请求头字段的数组
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            # 设置编码格式
            'Content-Type: application/json;charset=utf-8',
            'Content-Length: ' . strlen($postdata)
        )
   	);
	# curl_exec 执行一个cURL会话。
    $response = curl_exec($ch);
	if ($response === false) {
	    $error = curl_error($ch);
	    writeLog($wxid, ['error' => $error, 'url' => $url], "cURL 请求出错");
	}
	
	# 关闭一个cURL会话
    curl_close($ch);
    // $resa = json_decode($response, true);
    $resa = json_decode($response, true, 512, JSON_BIGINT_AS_STRING);
	
    if (strpos($url, "Login/CheckQR") !== false || strpos($url, "Login/TwiceAutoAuth") !== false || strpos($url, "Tools/setproxy") !== false) {
        return $resa;
    }
	
	global $systemData;
    if (isset($resa['Code'])&&$resa['Code']!=0) {
    	$mix+=1;
    	writeLog($wxid, ['data'=>$resa,'mix'=>$mix, 'params'=>$data, 'url'=>$url], "返回异常数据");
		if ($resa['Code'] == '-13') {
            TwiceAutoAuth($url, $wxid);
		}
		if ($resa['Code'] == '-1' && strpos($resa['Message'], '数据[DecryptData]失败') !== false && $mix >= 3) {
			writeLog($wxid, $resa, "多次失败停止".$mix);
			exit("多次失败停止".$mix);
		}
		if ($resa['Code'] == '-1' || $resa['Code'] == '-8') {
			writeLog($wxid, ["url"=>$url,'data'=>$resa], "异常返回");
			if(!is_dir('/tmp/log/response/')) mkdir("/tmp/log/response/",0777,true);
			file_put_contents("/tmp/log/response/{$wxid}.log", date("Y/m/d H:i:s")."异常返回"  .var_export($response, true)."\n链接6：".$url."参数：{$postdata}  mix: {$mix}"."\n\n", FILE_APPEND);
			if (strpos($resa['Message'], "登录失败") !== false) {
				writeLog($wxid, ['data'=>$resa], "二次登录失败，检测到退出，停止程序");
				exit("检测到退出，停止程序");
			}
		}
		if ($mix <= $systemData['RETRY_COUNT'] ) {
			writeLog($url, ['res'=>$resa,'cr'=>$mix, 'r'=>$systemData['RETRY_COUNT'], 'url'=>$url], "--不正常返回，重新获取{$mix}");
				sleep($systemData["retry_all_2"] + $mix);
				return posturl($url, $data, $mix);
		}
	} else if (empty($resa)) {
		$mix+=1;
		if ($mix <= $systemData['RETRY_COUNT']) {
			writeLog($url, ['res'=>$resa,'cr'=>$mix, 'r'=>$systemData['RETRY_COUNT'], 'url'=>$url], "--返回空-没拿到，重新获取{$mix}");
             sleep($systemData["retry_all_3"] + $mix);
            return posturl($url, $data, $mix);
        }
	}
	unset($data);
    return $resa;
}

// posturldel 删除请求
function posturldel($url,$postdata2 = [], $Maxid = "0", $cr=0){
	$wxid = $postdata2['Wxid'] ?? "init_wxid";
    $postdata = json_encode($postdata2);
    $postdata = str_replace('131230982',$Maxid,$postdata);

    # 初始化一个curl会话
    $ch = curl_init();
	# 启用时会发送一个常规的POST请求
    curl_setopt($ch, CURLOPT_POST, 1);
	# 需要获取的URL地址
    curl_setopt($ch, CURLOPT_URL, $url);

    curl_setopt($ch, CURLOPT_FRESH_CONNECT, true);
	curl_setopt($ch, CURLOPT_FORBID_REUSE, true);
    
	# 全部数据使用HTTP协议中的"POST"操作来发送。
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
	# 将curl_exec()获取的信息以文件流的形式返回，而不是直接输出。
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT,30);   //只需要设置一个秒的数量就可以 
	# 一个用来设置HTTP请求头字段的数组
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            # 设置编码格式
            'Content-Type: application/json;charset=utf-8',
            'Content-Length: ' . strlen($postdata)
        )
   	);
	# curl_exec 执行一个cURL会话。
    $response = curl_exec($ch);
	if ($response === false) {
	    $error = curl_error($ch);
	    writeLog($wxid, ['error' => $error, 'url' => $url], "cURL 请求出错");
	}

	# 关闭一个cURL会话
    curl_close($ch);
    $res1 = json_decode($response, true, 512, JSON_BIGINT_AS_STRING);
    // writeLog($wxid, $res1, "应牛老板要求 必须记上，所有请求");
    // file_put_contents("/tmp/log/exitlog/qie_{$GLOBALS['km']}.log", date("Y/m/d H:i:s")."请求返回"  .var_export($resa, true)."\n链接6：".$url."\n\n", FILE_APPEND);
    // 二次登录判断
	if (isset($res1['Code']) && $res1['Code'] == '-13') {
        TwiceAutoAuth($url, $wxid);
    }
	
	global $systemData;
	
	//应牛老板要求 
    if(strpos($res1['Message'], 'too many open files') !== false && $cr < $systemData['RETRY_COUNT']){
        writeLog($url, ['res'=>$res1,'cr'=>$cr, 'r'=>$systemData['RETRY_COUNT'],"postdata"=>$postdata], "--posturldel 10次没拿到，重新获取{$cr}");
    	$cr+=1;
    	sleep(3);
    	return posturldel($url, $postdata2, $Maxid, $cr);
    }

	
    if ( (!isset($res1['Code']) || $res1['Code'] != 0 || empty($res1)) && $cr < $systemData['RETRY_COUNT'] && $res1['Code'] == '-13') {
    	writeLog($proxy, ['res'=>$res1,'cr'=>$cr, 'r'=>$systemData['RETRY_COUNT']], "--posturldel 10次没拿到，重新获取{$cr}");
    	$cr+=1;
    	sleep(3);
    	return posturldel($url, $postdata2, $Maxid, $cr);
    }
    return $res1;
}

//getLikeList 自动点赞
function getLikeList($wxid, $proxy, $e_time, $type=5) {
	$url = 'http://'.$proxy.'/api/FriendCircle/GetList';
	$params = [
		'Fristpagemd5' => '',
		'Maxid' => 0,
		'Wxid' => $wxid,
	];
	global $systemData;
    // 将时间戳转换为当天凌晨的时间戳
    $midnightTimestamp = strtotime("today", time());
    // 计算凌晨2点的时间戳
    $twoAMTimestamp = strtotime("+2 hours", $midnightTimestamp);
    // 计算凌晨2点的时间戳
    $AMTimestamp = strtotime("+5 hours", $midnightTimestamp);
	while (true) {
		$timestamp = time();
		if ($timestamp >= $e_time) break;
		$res = posturldel($url, $params);
		if (isset($res['Code'])&&$res['Code']!=0) {
			if ($res['Code'] == '-13') {
				writeLog($proxy, ['wxid'=>$wxid,'data'=>$res], "获取朋友圈首页-检测到退出");
				exit("检测到退出，停止程序");
				break;
			}
			if ($res['Code'] == '-1' || $res['Code'] == '-8') {
				sleep($systemData["retry_all_3"]);
				$res = posturldel($url, $params);
			}
		}
		$objectList = $res['Data']['ObjectList'] ?? [];
		if ($objectList) {
			foreach ($objectList as $key => $value) {
				$id = $value['Id'];
				if (!$value['LikeFlag']) {
					$res = like($wxid, $proxy, $id, $systemData);
					if($res === true){
					    $newTimestamp = time() + 86400;
					    saveRedis($wxid,["type"=>$type,"time"=>$newTimestamp,"proxy"=>$proxy,"wxid"=>$wxid,"kmcode"=> $GLOBALS['km'],"gqtime"=>$e_time]);
					    writeLog($wxid,["codeName"=>$km,"proxy"=>$proxy,'wxid'=>$wxid,"id"=>$id],"朋友圈点赞：由于微信限制,请耐心等待24小时,满24小时自动开启");
					    exit("异常退出：由于微信限制，请耐心等待24小时，中间请勿退出MAC，满24小时自动开启");
					}
				}
			}
		}
		
		if($timestamp >= $twoAMTimestamp && $timestamp <= $AMTimestamp){
		    // 运行超频
		    $newTimestamp = time() + rand(60,1800);
		    saveRedis($wxid,["type"=>$type,"time"=>$newTimestamp,"proxy"=>$proxy,"wxid"=>$wxid,"gqtime"=>$e_time,"kmcode"=> $GLOBALS['km']]);
		    writeLog($wxid, ["proxy"=>$proxy,'wxid'=>$wxid,'response'=>$res,"kmcode"=>$km],"强制进行休眠操作：随机唤醒");
		    exit("异常退出：由于微信限制，请耐心等待24小时，中间请勿退出MAC，满24小时自动开启");
		}
		unset($objectList);
		unset($res);
		unset($timestamp);
		sleep($systemData["retry_all_15"]);
	}
}

//autoComments 自动评论
function autoComments($wxid, $proxy, $e_time, $type = 8){
	$url = 'http://'.$proxy.'/api/FriendCircle/GetList';
	$params = [
		'Fristpagemd5' => '',
		'Maxid' => 0,
		'Wxid' => $wxid,
	];

	global $systemData;
	// 将时间戳转换为当天凌晨的时间戳
	$midnightTimestamp = strtotime("today", time());
	// 计算凌晨2点的时间戳
	$twoAMTimestamp = strtotime("+2 hours", $midnightTimestamp);
	// 计算凌晨2点的时间戳
	$AMTimestamp = strtotime("+5 hours", $midnightTimestamp);
	
	$km = $GLOBALS['km'];
	$redis = newRedis();
	$redis->select(4);
	$content = $redis->get($km);
	$redis->close();
	while (true) {
		$timestamp = time();
		if ($timestamp >= $e_time && $e_time !=0){
		    writeLog($wxid,["wxid"=>$wxid,"url"=>$url,"params"=>$params,"e_time"=>$e_time],"验证码有效时间已过期，请重新登陆后再试！"); 
		    sendTextMsg($wxid, $proxy, "当前使用验证码已过期，请更换新验证码。");
		    exit("运行异常：验证码失效");
		}
		
		$res = posturldel($url, $params);
		if (isset($res['Code'])&&$res['Code']!=0) {
			if ($res['Code'] == '-13') {
				writeLog($proxy, ['wxid'=>$wxid,'data'=>$res], "获取朋友圈首页-检测到退出");
				exit("检测到退出，停止程序");
				break;
			}
			if ($res['Code'] == '-1' || $res['Code'] == '-8') {
				sleep($systemData["retry_all_3"]);
				$res = posturldel($url, $params);
			}
		}
		
		$objectList = $res['Data']['ObjectList'] ?? [];
		if ($objectList) {
			foreach ($objectList as $key => $value) {
				$id = $value['Id'];
				$isC = 1;
				if ($value['CommentCount'] > 0) {
					foreach ($value['CommentUserList'] as $k => $v) {
						if ($v['Username'] == $wxid) {
							$isC = 0;
							break;
						}
					}
				}
				if ($isC == 1) {
					$red = Comment($wxid, $proxy, $id, $content, $systemData);
					if($red === true){
					    //由于微信限制，请耐心等待24小时，中间请勿退出MAC，满24小时自动开启 sleep(86400);
					    $newTimestamp = time() + 86400;
					    saveRedis($wxid,["type"=>$type,"time"=>$newTimestamp,"kmcode"=>$km,"content"=>$content,"proxy"=>$proxy,"wxid"=>$wxid,"gqtime"=>$e_time]);
					    writeLog($wxid,["url"=>$url,'params'=>$params,'response'=>$res],"由于微信限制，请耐心等待24小时，24小时自动开启");
					    exit("异常返回-评论点赞微信限制，请耐心等待24小时");
					}
				}
			}
		}
	
		if($timestamp >= $twoAMTimestamp && $timestamp <= $AMTimestamp){
		    // 运行超频
		    $newTimestamp = time() + rand(60,1800);
		    saveRedis($wxid,["type"=>$type,"time"=>$newTimestamp,"kmcode"=>$km,"content"=>$content,"proxy"=>$proxy,"wxid"=>$wxid,"gqtime"=>$e_time]);
		    writeLog("正常请求","强制进行休眠操作：随机唤醒", ["url"=>$url,'params'=>$params,'response'=>$res]);
		    exit("异常退出：由于微信限制，请耐心等待24小时，中间请勿退出MAC，满24小时自动开启");
		}
	
		unset($objectList);
		unset($res);
		unset($timestamp);
		gc_collect_cycles();
		sleep($systemData["retry_all_15"]);
	}
}

//autoLikeAndComments 自动点赞+评论
function autoLikeAndComments($wxid, $proxy, $e_time, $type =9){
	$url = 'http://'.$proxy.'/api/FriendCircle/GetList';
	$params = [
		'Fristpagemd5' => '',
		'Maxid' => 0,
		'Wxid' => $wxid,
	];

	global $systemData;
	// 将时间戳转换为当天凌晨的时间戳
	$midnightTimestamp = strtotime("today", time());
	// 计算凌晨2点的时间戳
	$twoAMTimestamp = strtotime("+2 hours", $midnightTimestamp);
	// 计算凌晨2点的时间戳
	$AMTimestamp = strtotime("+5 hours", $midnightTimestamp);
	
	$km = $GLOBALS['km'];
	$redis = newRedis();
	$redis->select(4);
	$content = $redis->get($km);
	$redis->close();
	while (true) {
		$timestamp = time();
		if ($timestamp >= $e_time && $e_time != 0){
		    writeLog($url, ["url"=>$url,"wxid"=>$wxid,"e_time"=>$e_time], "验证码有效时间已过期，请重新登陆后再试！");
			sendTextMsg($wxid, $proxy, "当前使用验证码已过期，请更换新验证码。");
		    exit("运行异常：验证码失效");
		}
		
		$res = posturldel($url, $params);
		if (isset($res['Code'])&&$res['Code']!=0) {
			if ($res['Code'] == '-13') {
				writeLog($proxy, ['wxid'=>$wxid,'data'=>$res], "获取朋友圈首页-检测到退出");
				exit("检测到退出，停止程序");
				break;
			}
			if ($res['Code'] == '-1' || $res['Code'] == '-8') {
				sleep($systemData["retry_all_10"]);
				$res = posturldel($url, $params);
			}
		}
		
		$objectList = $res['Data']['ObjectList'] ?? [];
		if ($objectList) {
			foreach ($objectList as $key => $value) {
				$id = $value['Id'];
				if (!$value['LikeFlag']) {
					$res = like($wxid, $proxy, $id);
					if($res === true) {
					    //由于微信限制，请耐心等待24小时，中间请勿退出MAC，满24小时自动开启 sleep(86400);
					    $newTimestamp = time() + 86400;
					    saveRedis($wxid,["type"=>$type,"time"=>$newTimestamp,"kmcode"=>$km,"content"=>$content,"proxy"=>$proxy,"wxid"=>$wxid,"gqtime"=>$e_time]);
					    writeLog($url, ["url"=>$url,'wxid'=>$wxid,'code'=>$km,"id"=>$id],"由于微信限制，请耐心等待24小时,满24小时自动开启");
					    exit("异常返回-点赞朋友圈检测到退出");
					}
				}
				$isC = 1;
				if ($value['CommentCount'] > 0) {
					foreach ($value['CommentUserList'] as $k => $v) {
						if ($v['Username'] == $wxid) {
							$isC = 0;
							break;
						}
					}
				}
				if ($isC == 1) {
					$res = Comment($wxid, $proxy, $id, $content);
					if($res === true) {
					    //由于微信限制，请耐心等待24小时，中间请勿退出MAC，满24小时自动开启 sleep(86400);
					    $newTimestamp = time() + 86400;
					    saveRedis($wxid,["type"=>$type,"time"=>$newTimestamp,"kmcode"=>$km,"content"=>$content,"proxy"=>$proxy,"wxid"=>$wxid,"gqtime"=>$e_time]);
					    writeLog($url, ["url"=>$url,'wxid'=>$wxid,'code'=>$km,"id"=>$id],"由于微信限制，请耐心等待24小时,满24小时自动开启");
					    exit("异常返回-点赞朋友圈检测到退出");
					}
				}
				unset($isC);
				unset($id);
			}
		}
		
		// 运行超频
		if($timestamp >= $twoAMTimestamp && $timestamp <= $AMTimestamp){
		    $newTimestamp = time() + rand(60,1800);
		    saveRedis($wxid,["type"=>$type,"time"=>$newTimestamp,"kmcode"=>$km,"content"=>$content,"proxy"=>$proxy,"wxid"=>$wxid,"gqtime"=>$e_time]);
		    writeLog("正常请求","启动强制进行休眠操作：随机唤醒", ["url"=>$url,'wxid'=>$wxid,'code'=>$km,"id"=>$id]);
		    unset($objectList);
		    unset($res);
		    exit("启动休眠模式");
		}
		
		unset($objectList);
		unset($res);
		unset($timestamp);
		gc_collect_cycles();
		sleep($systemData["retry_all_15"]);
	}
}

//Comment 评论方法
function Comment($wxid, $proxy, $id, $content, $redisData=[]) {
	$url = 'http://'.$proxy.'/api/FriendCircle/Comment';
	$params = [
		'Content' => "{$content}",
		'Id' => $id,
		'ReplyCommnetId' => 0,
		'Type' => 2,
		'Wxid' => $wxid,
	];
	$res = posturl($url, $params, 5);
	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($proxy, ['wxid'=>$wxid,'data'=>$res], "评论朋友圈检测到退出");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep($redisData["retry_all_3"]);
			unset($res);
			$res = posturl($url, $params, 5);
		}
	}
	if (isset($res['Code']) && $res['Code']== "0" && $res['Data']['BaseResponse']['ret'] == "201") {
		sendTextMsg($wxid, $proxy, "由于微信限制，请耐心等待24小时，中间请勿退出MAC，满24小时自动开启");
		gc_collect_cycles();
		return true;
	}
	if (isset($res['Code']) && $res['Code']== "0" && $res['Data']['BaseResponse']['ret'] == "0") {
		$t = date("Y-m-d H:i:s");
		if (!isset($res['Data']['snsObject']['Nickname'])) return;
		$nickname = $res['Data']['snsObject']['Nickname'] ?? "";
		if ($nickname != "") {
			$str = "{$t} 成功评论了一条好友【{$nickname}】的朋友圈";
			sendTextMsg($wxid, $proxy, $str);
		}
		unset($nickname);
	}
	unset($res);
	gc_collect_cycles();
}

//like 点赞方法
function like($wxid, $proxy, $id, $redisData=[]) {
	$url = 'http://'.$proxy.'/api/FriendCircle/Comment';
	$params = [
		'Content' => "",
		'Id' => $id,
		'ReplyCommnetId' => 0,
		'Type' => 1,
		'Wxid' => $wxid,
	];
	$res = posturl($url, $params, 5);
	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($proxy, ['wxid'=>$wxid,'data'=>$res], "点赞朋友圈检测到退出");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep($redisData["retry_all_3"]);
			$res = posturl($url, $params, 5);
		}
	}
	if (isset($res['Code']) && $res['Code']== "0" && $res['Data']['BaseResponse']['ret'] == "201") {
		sendTextMsg($wxid, $proxy, "由于微信限制，请耐心等待24小时，中间请勿退出MAC，满24小时自动开启");
		return true;
	}
	if (isset($res['Code']) && $res['Code']== "0" && $res['Data']['BaseResponse']['ret'] == "0") {
		$t = date("Y-m-d H:i:s");
		$nickname = $res['Data']['snsObject']['Nickname'];
		$str = "【朋友圈点赞】记录\n {$t} 自动点赞了一条【{$nickname}】的朋友圈";
		sendTextMsg($wxid, $proxy, $str);
	}
	unset($res);
}

// getFavor 加载收藏
function getFavor($wxid, $proxy, $Keybuf="string", $data=[])
{
	$url = 'http://'.$proxy.'/api/Favor/Sync';
	$params = array(
		'Keybuf' => $Keybuf,
		"Wxid" => $wxid
	);
	$res = posturl($url, $params);
	if (isset($res['Data']['Ret']) && !empty($res['Data']['List'])) {
		$list = [];
		foreach ($res['Data']['List'] as $key => $value) {
			if ($value['Flag'] == 0) $list[] = $value;
		}
		$data = array_merge($data, $list);

		$n = count($data);
		sendTextMsg($wxid, $proxy, "已加载{$n}条收藏");
		sleep(1);
		return getFavor($wxid, $proxy, $res['Data']['KeyBuf']['buffer'], $data);
	}
	return $data;
}

// delFavor 删除收藏
function delFavor($wxid, $proxy, $data, $start, $end) {
	$url = 'http://'.$proxy.'/api/Favor/Del';
	$n = 0;
	foreach ($data as $key => $value) {

		if ($start < $value['UpdateTime'] && $value['UpdateTime'] < $end) {
			$params = [
				'FavId' => $value['FavId'],
				'Wxid' => $wxid
			];

			$res = posturl($url, $params);
			if (!empty($res['Data']) && $res['Data']['BaseResponse']['ret'] ==0) {
				$n++;
				if ($n % 10 == 0) sendTextMsg($wxid, $proxy, "已删除{$n}条收藏");
			}
		}
	}
	return $n;
}

 // saveRedis 超时用户休眠
function saveRedis($wxid, $data){
	// 拼装参数
	$userData = [
	  'time' => $data["time"],
	  'uuid' => $wxid,
	  'type' => $data["type"],
	  'kmcode' => isset($data["kmcode"]) ? $data["kmcode"] : "",
	  'proxy' => $data['proxy'],
	  'wxid' => $wxid,
	  "start_time" => isset($data["start_time"]) ? $data["start_time"] : "",
	  "end_time" => isset($data["end_time"]) ? $data["end_time"] : "",
	  "content" => isset($data["content"]) ? $data["content"] : "",
	  'gqtime' => isset($data["gqtime"]) ? $data["gqtime"] : "",
	  "bs_context"=> isset($data["bs_context"]) ? $data["bs_context"] : "",
	  "bs2_context"=> isset($data["bs2_context"]) ? $data["bs2_context"] : "",
	  "timing" => 1,
	  "key"=> $wxid,
	  "Maxid"=> isset($data["Maxid"]) ? $data["Maxid"] : "",
	  "delete_num"=> isset($data["delete_num"]) ? $data["delete_num"] : "",
	];
	
	$redis = newRedis();
	$redis->select(6);
    $redis->set($wxid . "<|>" . $userData["time"], json_encode($userData, true));
	$redis->close();
}

//delRedis 删除运行标记
function delRedis($wxid = "", $systemData = []){
    if($wxid == ""){
		ErrorMsg("删除用户运行任务失败：未获得用户id");
        return false;
    }
	$redis = newRedis($systemData);
    // 记录用户已启动进程
    $redis->select(7);
    if($redis->get($wxid)){
        $redis->del($wxid);
    }
	
	// 定义键的模式
	$pattern = $wxid.'*';
	$redis->select(6);
	$keys = $redis->keys($pattern);
	if (!empty($keys)) {
	    foreach ($keys as $key) {
	        $redis->del($key);
	    }
	} 
    $redis->close();
}

//init_Mysql 初始化信息
function init_Mysql($redis, $systemData= []){
 	$redis->select(10);
	$key = $redis->lPop($systemData['redis_key']);

	if(!$key){
		$redis->close();
		exit("无任务可操作\n");
	}
	
	$redis->select(11);
	$res = $redis->get($key);
	if(!$res){
		$redis->close();
		exit("无任务可操作\n");
	}
	
	//解析参数 
	$res = json_decode($res, true);
	if(!$res){
		$redis->close();
		exit("暂无数据");
	}
	
	// 检测文件夹是否存在
	$dir = "/tmp/tmp.log";
	if($systemData["log_address"]){
		$dir = $systemData["log_address"] . date("Ym") . "/" . date("d");
	}else{
		$dir = __DIR__ . "/" . date("Ym") . "/" . date("d");
	}
	if(!is_dir($dir)) mkdir($dir,0755,true);
	$fileNmae = $dir . "/" . $res['kmcode'] . ".log";

	// 其他地方使用验证码，需要设置一个全局
	$GLOBALS["fileDir"] = $dir;
	$GLOBALS["fileNameAddress"] = $fileNmae;
	$GLOBALS['km'] = $res['kmcode'];

	//记录运行启动 
	writeLog("运行任务启动", $res, "启动任务运行-");
	if(!$res['proxy']){
		$redis->close();
		writeLog("异常参数", $res, "任务运行失败，参数缺失～");
		exit("任务运行失败，参数缺失");
	}
	// 获取微信wxid
	if ($res['wxid'] == '') {
		$users = checkerm($res['uuid'], $res['proxy'], $res, $redis, $systemData);
		if ($users) {
			$wxid = $users['userName'];
			$res["wxid"] = $wxid;
			$res['islogin'] = 1;

			if($systemData["current_service"] == 2){
				// 同步机制
			 	posturl($systemData["current_service_address"], [
			 		'type'=>2, 
			 		'km_json'=>json_encode($res),
			 		'key'=>$wxid,
			 		'value'=>$res['proxy']."@".$wxid, 
			 		'uuid'=>$res["uuid"]
			 	]);
			}else{	
				// 存放登录信息
				$redis->select(12);
				$redis->set($res["uuid"], json_encode($res));
				$redis->expire($res["uuid"], 300);
			}
		} 
		if (empty($users)){
			$redis->close();
			exit($key."：未检测到登录用户");
		}

		if ($wxid) {
			$res["gqtime"] = checkCodeAndBind($wxid, $res['proxy'], $res, $redis, $systemData);
		}
	} else {
		$wxid = $res['wxid'];
		$res["gqtime"] = DetectingUserStatus($wxid, $res['proxy'], $res, $redis);
	}
	// $redis->close();待检测
	
	if (!$res["gqtime"]){
		ErrorMsg("验证验证码时间未通过验证", $res);
		exit($key."：检测卡密未通过");
	}
    return $res;
}

//DetectingUserStatus  检测用户激活码
function DetectingUserStatus($wxid, $proxy, $res=[], $redis){
    $values = $proxy."@".$wxid;
    $redis->select(13);
    $redis->set($wxid, $values);
    
	$row = newMysql('select * from a_app_dailicode2 where code=\'' . $res['kmcode'] . '\'', 1);
	if (empty($row)) {
		sendTextMsg($wxid, $proxy, "未取得数据，程序终止。");
		writeLog($proxy,["codeName"=>$res['kmcode'],'wxid'=>$wxid],"未取得数据，程序终止。");
		exit("未取得数据，程序终止。");
	}

    //判断卡密是否过期
    if ($row['gqtime'] != 0 && time() >= $row['gqtime']) {
        //此激活码已过期;
		sendTextMsg($wxid, $proxy, "激活码过期，禁止使用。");
		writeLog($res['kmcode'], ["codeName"=>$res['kmcode'],'wxid'=>$wxid,"row"=>$row],"激活码过期，禁止使用。");
		exit("激活码过期，禁止使用。");
    }

	//卡密封停终止程序
    if ($row['state'] == 2) {
		sendTextMsg($wxid, $proxy, "激活码冻结，禁止使用。");
		writeLog($proxy,["codeName"=>$res['kmcode'],'wxid'=>$wxid,"row"=>$row],"激活码冻结，禁止使用。");
		exit("激活码冻结，禁止使用。");
    }

    return $row['gqtime'];
}

// newMysql 执行数据库命令
function newMysql($sql, $type=1){
	global $systemData;
	
    // 初始化信息
    $mysqlInfo = $systemData["mysql"];
	
	// 创建数据库连接
	$mysql_li = new mysqli($mysqlInfo["dbhost"], $mysqlInfo["username"], $mysqlInfo["pwd"], $mysqlInfo["dbname"], $mysqlInfo["port"]);
	// 检查连接是否成功
	if ($mysql_li->connect_error) {
		//记录运行启动
		writeLog("连接MYSQL错误", $sql , "连接mysql失败：". $mysql_li->connect_error);
	    exit("连接失败");
	}
	
	unset($mysqlInfo);
	$result = $mysql_li->query($sql);
	// 查询数据
	if($type == 1){
		    // 将结果集转换为索引数组
		if ($result->num_rows > 0) {
		    $row = $result->fetch_assoc();
			$mysql_li->close();
			return $row;
		} else {
			$mysql_li->close();
		   return [];
		}
	}
	
	if ($result === TRUE) {
		$mysql_li->close();
		return true;
	}
	$mysql_li->close();
	return false;
}

// newRedis 初始化redis
function newRedis($systemData = []){
	// 初始化信息
    if(empty($systemData)) global $systemData;
	
	$redisData = [];
	if($systemData["current_service"] == 1){
		$redisData = $systemData["master_redis"];
	}else{
		$redisData = $systemData["assistant_redis"];
	}
	
    $redis = new Redis();
	$redis->connect($redisData["host"], $redisData["port"]);
	if($redisData["auth"]){
		$redis->auth($redisData["auth"]);
	}
	
	unset($redisData);
	return $redis;
}

function ErrorMsg($msg="", $data=[]){
	// 获取当前时间
	$time = date('Y-m-d H:i:s');
	// 创建日志条目的基础信息
	$logArr = "{$time} 任务运行异常：{$msg}";
	// 添加参数（如果有的话）
	if (!empty($data)) {
	    $logArr .= ' ' . json_encode($data, JSON_UNESCAPED_UNICODE);
	}
	// 添加换行符
	$logArr .= PHP_EOL;
	
	// 存放日志地址
	$fileAddress =  $GLOBALS["fileDir"] . "/error.log";
	file_put_contents($fileAddress, $logArr, FILE_APPEND);
	unset($logArr);
	unset($fileAddress);
}
