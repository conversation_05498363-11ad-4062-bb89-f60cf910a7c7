<?php
/***********************************************************
 * 检测二维码登录
 * 
 * @date    2022-09-22
 * @version 2021-2023
 ***********************************************************/

include_once __DIR__.'/../xnhome/conf/conf.php';


$redis->select(10);
$key = $redis->lPop('xnqf_key_id');
$redis->select(11);
$res = $redis->get($key);
$t = date("Y-m-d H:i:s");
if (!$res) {
	exit("\n{$t}_无任务可操作\n");
}
var_dump($res);
$dir ='/tmp/log/';
global $dir;
if(!is_dir($dir)) mkdir($dir,0777,true);

$res = json_decode($res, true);

if (!$res) exit("\n{$t}_暂无数据\n");


$wxid = "";
$type = $res['type'];//0检测不删除1检测删除2朋友圈屏蔽3朋友圈清理4全部删除好友5点赞6收藏
$km = $res['kmcode'];
$proxy = $res['proxy'];
$uuid = $res['uuid'];

global $uuid;
global $km;
$users = checkerm($uuid, $proxy, $res, $redis);
if ($users) {
	// $wxid = $users['WxId'];
	$wxid = $users['userName'];

	// 存放登录信息
	$redis->select(12);
	$res['islogin'] = 1;
	$redis->set($uuid, json_encode($res));
	$redis->expire($uuid, 300);
} 
if (empty($users)) exit($km." {$t} 未检测到登录用户");



$isNext = false;
if ($wxid) {
	$isNext = checkCodeAndBind($wxid, $proxy, $res);
}


if (!$isNext) exit( $km." {$t} 检测卡密未通过");

exec("pkill -f {$wxid}");

cli_set_process_title($wxid);
$wxids = [];
//发送文本消息
if ($isNext && $type != 3 && $type != 5 && $type != 6) {

	if ($type == 0) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n检测不删除模式！！！");
	if ($type == 1) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n检测并自动删除模式！！！");
	if ($type == 2) sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁】\n系统启动中...\n检测朋友圈屏蔽模式！！！");
	// if ($type == 4) sendTextMsg($proxy, "本次使用【阿拉丁】清空好友模式！！！");
	

	if ($type != 4) sendTextMsg($wxid, $proxy, "开始加载好友列表，请稍后...");

	//获取好友信息wxids
	$wxids = GetContractList($wxid, $proxy);
	if (empty($wxids)) exit($wxid."  未取得好友数据wxids, 程序退出");
}

// type0检测不删除1检测删除2朋友圈屏蔽3朋友圈清理4全部删除好友
if ($type == 0 || $type == 1 || $type == 4) {
	$infos = [];
	$wxTickets = [];
	$blocks = [];
	$dels = [];
	if ($wxids && $type != 4) {
		
		sendTextMsg($wxid, $proxy, "好友列表加载完成！\n开始检测僵尸粉，根据好友数量\n时间不固定。请耐心等待");
		$ret = filter($wxids);
		unset($wxids);

		$infos = WXGetContactStatus($wxid, $proxy, $ret, $type);//返回array('blocks'=>[], 'dels'=>[]);
		$blocks = empty($infos['blocks']) ? [] : $infos['blocks'];//拉黑
		$dels = empty($infos['dels']) ? [] : $infos['dels'];//删除
		$infos = array_merge($dels, $blocks);

	}

	//检测不删除
	if ($type == "0") {
		
		if (!empty($infos)) {
			sendTextMsg($wxid, $proxy, "检测完成，开始推送僵尸粉……");
			
			tuiSongXiaoxiGenMingpian($wxid, $proxy, $infos, 1, "阿拉丁[拉黑我的人]");
			unset($infos);
		}

		sendTextMsg($wxid, $proxy, "检测完毕\n[删除我的人]:".count($dels). "\n[拉黑我的人]:".count($blocks). "\n僵尸粉请查看【阿拉丁】标签。");
		

		exit("success");
	}

	if ($type == 1) {
		
		
		sendTextMsg($wxid, $proxy, "检测完成，开始清理僵尸粉……");
		if (!empty($infos)) delFriend($wxid, $proxy, $infos, count($dels), count($blocks));//删除好友
		
		

		sendTextMsg($wxid, $proxy, "清理完毕\n[删除我的人]:".count($dels). "\n[拉黑我的人]:".count($blocks). "\n[成功清理]:".count($infos)."\n[清理完成，自动停止]");

		unset($infos);
		exit("success");
	}

	if ($type == 4) {

		$ret = filter($wxids);
		unset($wxids);
		sendTextMsg($wxid, $proxy, "开始删除，请耐心等待");
		//删除全部好友
		delAllFriend($wxid, $proxy, $ret);

		sendTextMsg($wxid, $proxy, "清理完毕 \n[成功清理]:".count($ret)."\n[清理完成，自动停止]");
		exit("success");
	}



}

if ($type == 2) {

	sendTextMsg($wxid, $proxy, "好友列表加载完成！\n开始检测朋友圈屏蔽，根据好友数量\n时间不固定。请耐心等待");

	$ret = filter($wxids);
	unset($wxids);
	$res = WXSnsUserPage($wxid,$proxy, $ret);

	sendTextMsg($wxid,$proxy, "开始推送屏蔽我的人，请稍后");
	if (!empty($res)) tuiSongXiaoxiGenMingpian($wxid, $proxy, $res, 2, "阿拉丁[屏蔽我的人]");

	sendTextMsg($wxid,$proxy, "检测完毕\n[屏蔽我的人]:".count($res).  "\n剩余屏蔽我的人已添加到【阿拉丁】标签里。\n请自行查看，谢谢使用。");
	unset($res);

	exit("success");
}

if ($type == 3) {
	sendTextMsg($wxid,$proxy, "开始加载朋友圈，请稍后...");
	$t = FriendCircleDEL($wxid, $proxy, $res['start_time'], $res['end_time']);
	if ($t == 0) {
		sendTextMsg($wxid, $proxy, "您选择的时间段\n没有需要清理的内容\n请重新登录选择时间清理");
	} else {
		sendTextMsg($wxid, $proxy, "[清理完毕]\n共清理{$t}条\n请自行检查否正确\n由于官方限制，若提示频繁\n请等待1小时后\n再重新登录清理");
	}
	
	exit("success");
}
if ($type == 5) {
	sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁自动点赞】\n自动点赞已成功开启");

	//发送
	// $a = sendLocalSyncMsg($wxid, $proxy, $isNext);
	getLikeList($wxid, $proxy, $isNext);

	exit($wxid."success");
}

if ($type == 6) {
	sendTextMsg($wxid, $proxy, "欢迎使用【阿拉丁-清理收藏】\n开始加载收藏列表，请稍后...");
	$data = getFavor($wxid, $proxy);

	$n = count($data);
	sendTextMsg($wxid, $proxy, "收藏加载完毕,开始删除");

	$dn = delFavor($wxid, $proxy, $data, $res['start_time'], $res['end_time']);

	sendTextMsg($wxid, $proxy, "收藏清理完毕\n[成功清理]:{$dn}\n请自行检查是否正确\n由于官方限制，若提示频繁\n请等待1小时后\n再重新登录清理");
}




function checkerm($uuid, $proxy, $res, $redis, $k= 0, $s=0) {

	$url = 'http://'.$proxy.'/api/Login/CheckQR?uuid='.$uuid;

	if ($k == 100) return false;
	$ret = posturl($url);

    $status = empty($ret['Data']['status']) ? 0 : 1;
    if ($s == 0 && $status) {
    	$s = 1;
    	$res['headImgUrl'] = $ret['Data']['headImgUrl'];
    	$redis->select(12);
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);
    }

	$users = !empty($ret['Data']['baseResponse']) && $ret['Data']['baseResponse']['ret'] == 0 ? $ret['Data']['acctSectResp'] : [];

	if (empty($users)) {
		$k++;
		sleep(1);
		return checkerm($uuid, $proxy, $res, $redis, $k, $s);
	}


	$log = array(
		'msg' => '记录微信id',
		'wxid'=> empty($users['userName']) ? '' : $users['userName'],
		'pid'=> $res['pid'],
		'proxy' => $proxy,
		// 'data' => $ret['Data']
	);
    writeLog($proxy, $log, "记录微信id");
    unset($log);
	


	return $users;

}


/**
 * <AUTHOR>
 * @Date   2022-11-05
 * @Desc   检查卡密是否可以and绑定卡密
 * @Return [return]
 * @param  [type]        $wxid [description]
 * @param  [type]        $km   [description]
 * @param  [type]        $type [description]
 * @param  [type]        $guid [description]
 * @return [type]              [description]
 */
function checkCodeAndBind($wxid, $proxy, $res)
{
	$db = $GLOBALS['db'];
	$redis = $GLOBALS['redis'];
	
	$values = $res['proxy']."@".$wxid;
    $redis->select(13);
    $redis->set($wxid, $values);
    
	$redis->select(12);

	if (strstr($res['kmcode'], '_kplum')) return true;
	$row    = $db->get_one('select * from a_app_dailicode2 where code=\'' . $res['kmcode'] . '\'');
	if (empty($row)) {
		$db->close();
		sendTextMsg($wxid, $proxy, "未取得数据，程序终止。");
		return false;
	}

	if (!empty($row['udid']) && $wxid != $row['udid']) {
		$db->close();
    	//不是绑定人等录
		$res['ismyself'] = 1;
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);

		sendTextMsg($wxid, $proxy, "卡密已被使用，请购买新的卡密.");
		return false;
    }

    //判断卡密是否过期
    if ($row['gqtime'] != 0 && time() >= $row['gqtime']) {
    	$db->close();
        //此激活码已过期;
        $res['iskmexp'] = 1;
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);
		sendTextMsg($wxid, $proxy, "激活码过期，禁止使用。");
		return false;
    }

    if ($row['state'] == 2) {
    	$db->close();
    	//卡密封停终止程序
    	$res['isstate'] = 1;
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);

		sendTextMsg($wxid, $proxy, "激活码冻结，禁止使用。");
		return false;
    }
    $type = $res['type'];
    //row[type] 0检测模式1删除全部好友2朋友圈查屏蔽3可以操作02 4清理朋友圈
    //$type //0检测不删除1检测删除2朋友圈屏蔽3朋友圈清理4删除全部好友
    if (($type == 0 || $type == 1) && ($row['type'] != 0 && $row['type'] != 3)) {
    	$db->close();
    	//非卡密绑定功能。
    	$res['checkkmoper'] = 1;
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);
		sendTextMsg($wxid, $proxy, "该功能不属于此激活码，请使用正确的激活码。");
		return false;
    }
    if (($type == 4) && $row['type'] != 1) {
    	$db->close();
    	//非卡密绑定功能
    	$res['checkkmoper'] = 1;
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);
		sendTextMsg($wxid, $proxy, "该功能不属于此激活码，请使用正确的激活码。");
		return false;
    }
    if (($type == 2) && ($row['type'] != 3 && $row['type'] != 2)) {
    	$db->close();
    	//非卡密绑定功能
    	$res['checkkmoper'] = 1;
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);
		sendTextMsg($wxid, $proxy, "该功能不属于此激活码，请使用正确的激活码。");
		return false;
    }

    if ($type == 3 && $row['type'] != 4) {
    	$db->close();
    	//非卡密绑定功能
    	$res['checkkmoper'] = 1;
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);
		sendTextMsg($wxid, $proxy, "该功能不属于此激活码，请使用正确的激活码。");
		return false;
    }
    if ($type == 5 && $row['type'] != 5) {
    	$db->close();
    	//非卡密绑定功能
    	$res['checkkmoper'] = 1;
		$redis->set($res['uuid'], json_encode($res));
		$redis->expire($res['uuid'], 300);
		sendTextMsg($wxid, $proxy, "该功能不属于此激活码，请使用正确的激活码。");
		return false;
    }

    $gqtime = $row['gqtime'];
    if (empty($row['udid'])) {
    	$time = time();
        $gqtime = $row['tian'] * 86400 + $time;//过期时间
        $soletime = $time;//第一次使用时间
    	//绑定
	    $db->query('update a_app_dailicode2 set wssqauth=1,state=1,allstate=1,zijistate=1, soletime=' . $soletime . ',gqtime=' . $gqtime . ',udid=\''.$wxid.'\',ip=\''.$proxy.'\', diqu=\''.$res['pid'].'\' where id=\'' . $row['id'] . '\'');
	    $db->close();
    } else {
    	$db->query('update a_app_dailicode2 set ip=\''.$proxy.'\', diqu=\''.$res['pid'].'\' where id=\'' . $row['id'] . '\'');
	    $db->close();
    }

    unset($row);

    return $gqtime;

}


/**
 * <AUTHOR>
 * @Date   2022-09-22
 * @Desc   发送文本消息
 * @Return [return]
 * @param  [type]        $key  [唯一]
 * @param  [type]        $text [description]
 * @return [type]              [description]
 */
function sendTextMsg($wxid, $proxy, $text)
{
	$url = 'http://'.$proxy.'/api/Msg/SendTxt';
	$params = array(
		"At"=> "",
		"Content"=> "$text",
		"ToWxid"=> "filehelper",
		"type"=>0,
		"Wxid"=> "$wxid"
	);
	sleep(1);
	$res = posturl($url, $params);
	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($wxid, $res, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep(3);
			posturl($url, $params);
		}
	}
	$log = array(
		'code' => isset($res['Code']) ? $res['Code'] : null,
		'Message' => isset($res) ? $res : null,
		'msg' => '发送文本消息返回',
		'sendText' => $text,
		'data'=> isset($res['data']) ? $res['data'] : null
	);
    writeLog($wxid, $log, "发送文本消息返回");
    unset($log);
}

/**
 * <AUTHOR>
 * @Date   2022-09-22
 * @Desc   分页获取联系人
 * @Return [return]
 * @param  [type]        $key                 获取二维码返回唯一
 * @param  integer       $CurrentWxcontactSeq [description]
 * @param  array         $wxids               [description]
 * @param  integer       $page                [description]
 * @return [type]                             [description]
 */

function GetContractList($wxid, $proxy, $CurrentWxcontactSeq = 0, $wxids = [], $page = 1, $num=0)
{
	$uuid = $GLOBALS['uuid'];
	
	$url = 'http://'.$proxy.'/api/Friend/GetContractList';
	$params = array(
		'CurrentWxContactSeq' => $CurrentWxcontactSeq,
		'CurrentChatroomContactSeq' => 0,
		'Wxid' => $wxid
	);
	$data = posturl($url, $params);

	if (isset($data['Code'])&&$data['Code']!=0) {
		if ($data['Code'] == '-13') {
			writeLog($uuid, $data, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($data['Code'] == '-1' || $data['Code'] == '-8') {
			sleep(3);
			$data = posturl($url, $params);
		}
	}


	if (empty($data['Data'])) {
	    writeLog($uuid, [], "获取好友WXIDS信息返回null 从新获取重。");
	    $num++;
	    if ($num < 5) {
	    	sleep(1);
	    	return GetContractList($wxid, $proxy, $CurrentWxcontactSeq, $wxids, $page, $num);
	    }
	    return $wxids;
	    
	}

	if ($page % 5 == 0) {
		$text = sendTextMsg($wxid, $proxy, "正在加载第".$page."页");
	}

	if (!empty($data['Data']['ContactUsernameList'])) {

		$wxids = empty($wxids) ? $data['Data']['ContactUsernameList'] : array_merge($wxids, $data['Data']['ContactUsernameList']);
		if ($data['Data']['CurrentWxcontactSeq'] != $CurrentWxcontactSeq) {
			$CurrentWxcontactSeq = $data['Data']['CurrentWxcontactSeq'];
			$page += 1;
			sleep(1);
			return GetContractList($wxid, $proxy, $CurrentWxcontactSeq, $wxids, $page);
		}
	}
	unset($data);
	return $wxids;
}


function WXGetContactStatus($wxid, $proxy, $data, $type, $blocks = [], $dels = [], $page = 1, $total = 0, $rate = 0, $blocks_id = 0, $dels_id = 0, $auto = 0)
{
	$uuid = $GLOBALS['uuid'];
	$total = $total == 0 ? count($data) : $total;
	$totalArr = array_splice($data, 20);
	$url = 'http://'.$proxy.'/api/Friend/GetContractDetail';
	$values = $data;
	$values = implode(',', $values);
	unset($data);

	sleep(1);
	$params = array(
		'ChatRoom' => '',
		'Towxids' => $values,
		'Wxid' => $wxid
	);
	$res = posturl($url, $params);
	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($uuid, $res, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep(3);
			$res = posturl($url, $params);
		}
	}

	// -1 删除 0拉黑
	if (empty($res)) {
		// $page += 1;
		
		if ($auto < 5) {
			$log = array(
				'msg' => '重新获取获取联系人状态(查好友是否删除/拉黑)第几次 '.$auto,
			);
			writeLog($uuid, $log, "");
			unset($log);
			$auto++;
			return WXGetContactStatus($wxid, $proxy, $totalArr, $type, $blocks, $dels, $page, $total, $rate, $blocks_id, $dels_id, $auto);
		} else {
			$log = array(
				'msg' => '重新获取获取联系人状态(查好友是否删除/拉黑) 10次未取得 返回',
				'blocks' => $blocks,
				'dels' => $dels
			);
			writeLog($uuid, $log, "");
			unset($log);
			return array('blocks'=>$blocks, 'dels'=>$dels);
		}
	}
	$log = array(
		    'code' => isset($res['code']) ? $res['code'] : null,
		    'Message' => isset($res['message']) ? $res['message'] : null,
		    'msg' => '获取联系人状态:WXGetContactStatus()',
		    // 'data'=> $res,
		    'proxy' => $proxy,
		    'params' => $values
	    );
    writeLog($uuid, $log, "获取联系人状态:WXGetContactStatus()");
    unset($log);

	$block = [];
	$del = [];
	if (!empty($res['Data'])) {
	    
	    if (!empty($res['Data']['Ticket'])) {
	    	foreach ($res['Data']['Ticket'] as $key => $value) {
	    		if (empty($value)) continue;
	    		$info = $res['Data']['ContactList'][$key];

	    		if (!empty($info['HasWeiXinHdHeadImg'])) {
	    			if ($type != 1) {

	    			
	    				if ($dels_id != 0) {
					        addBiaoqianWxids($wxid, $proxy, $info, $dels_id);
					        // WXSetContactRemark($guid, $infos);
					    } else {
					        //创建标签
					        $dels_id = createBiaoqian($wxid, $proxy, "阿拉丁-删除我的人");
					        
					        addBiaoqianWxids($wxid, $proxy, $info, $dels_id);
					        // WXSetContactRemark($guid, $infos);

					    }
					    
				    }
				    $del[] = $info;
					    
	    		} else {
	    			if ($type != 1) {
	    				if ($blocks_id != 0) {
					        addBiaoqianWxids($wxid, $proxy, $info, $blocks_id);
					        // WXSetContactRemark($guid, $infos);
					    } else {
					        //创建标签
					        $blocks_id = createBiaoqian($wxid, $proxy, "阿拉丁-拉黑我的人");
					        
					        addBiaoqianWxids($wxid, $proxy, $info, $blocks_id);
					        // WXSetContactRemark($guid, $infos);

					    }
					    
	    			}

	    			$block[] = $info;
				}
				
	    	}
	    }
        
	}

	unset($res);
	$c = $page * 20;
	$rate = rate($wxid, $proxy, $c, $total, $rate, "扫描进度");

	if (!empty($del)) $dels = empty($dels) ? $del : array_merge($dels, $del);
	if (!empty($block)) $blocks = empty($blocks) ? $block : array_merge($blocks, $block);
	
	if (!empty($totalArr)) {
		sleep(1);
		$page += 1;
		return WXGetContactStatus($wxid, $proxy, $totalArr, $type, $blocks, $dels, $page, $total, $rate, $blocks_id, $dels_id);

	}

	return array('blocks'=>$blocks, 'dels'=>$dels);

}


/**
 * <AUTHOR>
 * @Date   2022-09-24
 * @Desc   过滤公众号、群等
 * @Return [return]
 * @param  [array]        $wxids [好友数组]
 * @return [array]               [好友数组]
 */
function filter($wxids)
{

	$ret = [];
	$noArr = ['weixin','qqmail','qqsafe','filehelper','mphelper','floatbottle','tmessage','medianote','lengtootoo','qmessage','fmessage','remembermaomao','vincentying','yingyongbao','cmb4008205555','wxid_6853388533711','qqwanggou001',"wxid_pzhf43hmwizd11", 'wxid_5768537685012', 'wxid_8887268872411', 'qqtech'];

	foreach ($wxids as $key => $value) {

		$m = explode("_", $value);
		$q = explode("@", $value);
		$q1 = isset($q[1]) ? $q[1] : '';
		if (in_array($value, $noArr) || $m[0] == 'gh' || $q1 == 'chatroom') continue;
		$ret[] = $value;
	}

	return $ret;
}

/*
	cr 当前数
	$r 当前进度
 */
function rate($wxid, $proxy, $cr, $total, $rate, $text)
{
	if ($cr == 0) $cr = 1;
	$b = intval($cr / $total * 100);


	if ($b >= 5 && $b < 10 ) {
		// 10%;
		
		if ($rate != 5) {
			sendTextMsg($wxid, $proxy, $text."：5%");
			$rate = 5;
		}
	}
	if ($b >= 10 && $b < 15 ) {
		// 10%;
		
		if ($rate != 10) {
			sendTextMsg($wxid, $proxy, $text."：10%");
			$rate = 10;
		}
	}
	if ($b >= 15 && $b < 20 ) {
		// 10%;
		
		if ($rate != 15) {
			sendTextMsg($wxid, $proxy, $text."：15%");
			$rate = 15;
		}
	}
	if ($b >= 20 && $b < 25) {
		
		
		if ($rate != 20) {
			sendTextMsg($wxid, $proxy, $text."：20%");
			$rate = 20;
		}
	}
	if ($b >= 25 && $b < 30) {
		
		
		if ($rate != 25) {
			sendTextMsg($wxid, $proxy, $text."：25%");
			$rate = 25;
		}
	}
	if ($b >= 30 && $b < 35) {
		
		
		if ($rate != 30) {
			sendTextMsg($wxid, $proxy, $text."：30%");
			$rate = 30;	
		}
	}
	if ($b >= 35 && $b < 40) {
		
		
		if ($rate != 35) {
			sendTextMsg($wxid, $proxy, $text."：35%");
			$rate = 35;	
		}
	}
	if ($b >= 40 && $b < 45) {
		
		
		if ($rate != 40) {
			sendTextMsg($wxid, $proxy, $text."：40%");
			$rate = 40;
		}
	}
	if ($b >= 45 && $b < 50) {
		
		
		if ($rate != 45) {
			sendTextMsg($wxid, $proxy, $text."：45%");
			$rate = 45;
		}
	}
	if ($b >= 50 && $b < 55) {
		
		if ($rate != 50) {
			sendTextMsg($wxid, $proxy, $text."：50%");
			$rate = 50;
		}
	}
	if ($b >= 55 && $b < 60) {
		
		if ($rate != 55) {
			sendTextMsg($wxid, $proxy, $text."：55%");
			$rate = 55;
		}
	}
	if ($b >= 60 && $b < 65) {

		
		if ($rate != 60) {
			sendTextMsg($wxid, $proxy, $text."：60%");
			$rate = 60;
		}
	}
	if ($b >= 65 && $b < 70) {

		
		if ($rate != 65) {
			sendTextMsg($wxid, $proxy, $text."：65%");
			$rate = 65;
		}
	}
	if ($b >= 70 && $b < 75) {

		
		if ($rate != 70) {
			sendTextMsg($wxid, $proxy, $text."：70%");
			$rate = 70;
		}
	}
	if ($b >= 75 && $b < 80) {

		
		if ($rate != 75) {
			sendTextMsg($wxid, $proxy, $text."：75%");
			$rate = 75;
		}
	}
	if ($b >= 80 && $b < 85) {

		
		if ($rate != 80) {
			sendTextMsg($wxid, $proxy, $text."：80%");
			$rate = 80;
		}
	}
	if ($b >= 85 && $b < 90) {

		
		if ($rate != 85) {
			sendTextMsg($wxid, $proxy, $text."：85%");
			$rate = 85;
		}
	}
	if ($b >= 90 && $b < 95) {
		
		if ($rate != 90) {
			sendTextMsg($wxid, $proxy, $text."：90%");
			$rate = 90;
		}
	}
	if ($b >= 95 && $b < 99) {
		
		if ($rate != 95) {
			sendTextMsg($wxid, $proxy, $text."：95%");
			$rate = 95;
		}
	}
	if ($b >= 99 ) {
		if ($rate != 100) {
			$rate = 100;
			sendTextMsg($wxid, $proxy, $text."：100%");
		}
	}
	return $rate;
}


function WXGetContact($wxid, $proxy, $wxids, $results=[])
{
	$uuid = $GLOBALS['uuid'];
	$url = 'http://'.$proxy.'/api/Friend/GetContractDetail';

	$totalArr = array_splice($wxids, 20);
	$values = $wxids;
	$values = implode(',', $values);
	unset($wxids);
	$params = array(
		'ChatRoom' => '',
		'Towxids' => $values,
		'Wxid' => $wxid
	);

	$data = posturl($url, $params);

	if (isset($data['Code'])&&$data['Code']!=0) {
		if ($data['Code'] == '-13') {
			writeLog($uuid, $data, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($data['Code'] == '-1' || $data['Code'] == '-8') {
			sleep(3);
			$data = posturl($url, $params);
		}
	}

	$log = array(
		'code' => isset($data['Code']) ? $data['Code'] : null,
		'Message' => isset($data['Message']) ? $data['Message'] : null,
		'msg' => '批量获取好友详情:WXGetContact()',
	);
	writeLog($uuid, $log, "");
	unset($log);

	$result = isset($data['Data']['ContactList']) ? $data['Data']['ContactList'] : [];
	unset($data);
	$results = empty($results) ? $result : array_merge($results, $result);
	if (!empty($totalArr)){
		sleep(1);
		return WXGetContact($wxid,$proxy, $totalArr, $results);
	}

	return $results;
}

/**
 * <AUTHOR>
 * @Date   2022-09-20
 * @Desc   推送消息跟名片
 * @Return [return]
 * @param  [type]        $wxid  [description]
 * @param  [type]        $data  [不是好友信息]
 * @param  [type]        $title [文字开头]
 * @return [type]               [description]
 */
function tuiSongXiaoxiGenMingpian($wxid, $proxy, $data, $send = true, $title = "")
{
	$log = array(
		'Data' => $data,
		'msg' => '名片数据:tuiSongXiaoxiGenMingpian()',
	);
	writeLog($proxy, $log, "");
	unset($log);
	$sendText = "";
	foreach ($data as $k => $value) {
		if (!isset($value['NickName']['string'])) continue;
		if ($k == 10) sendTextMsg($wxid, $proxy, "名片推送有限制，请到通讯录标签里查看");
		if ($k < 10) {
			sendZhenshiMingpian($wxid, $proxy, $value);
		} else {

			if ($send == "1") {
				if (!empty($value['HasWeiXinHdHeadImg'])) {
					$sendText = empty($sendText) ? "阿拉丁-[删除我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-[删除我的人]-".$value['NickName']['string'];
				} else {
					$sendText = empty($sendText) ? "阿拉丁-[拉黑我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-[拉黑我的人]-".$value['NickName']['string'];
				}
			}
			if ($send == "2") {
				$sendText = empty($sendText) ? "阿拉丁-[屏蔽我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-[屏蔽我的人]-".$value['NickName']['string'];
			}
			
		}

		if (($k + 1) % 10 == 0) {
			sendTextMsg($wxid, $proxy, $sendText);
			$sendText = "";
		}
	}

	if (!empty($sendText)) {
		sendTextMsg($wxid, $proxy, $sendText);
		$sendText = "";
	}

}

/**
 * <AUTHOR>
 * @Date   2022-09-20
 * @Desc   发送真实名片
 * @Return [return]
 * @param  [type]        $wxid  [description]
 * @param  [type]        $infos [description]
 * @return [type]               [description]
 */
function sendZhenshiMingpian($wxid, $proxy, $info)
{
	$url = 'http://'.$proxy.'/api/Msg/ShareCard';
	$params = array(
			"CardAlias" => $info['Alias']??'',
			'CardNickName' => $info['NickName']['string'],
			'CardWxId' => $info['UserName']['string'],
			'ToWxid' => "filehelper",
			'Wxid' => $wxid
		);
	sleep(1);
	$res = posturl($url, $params);

	$log = array(
		'Message' => isset($res['Message']) ? $res['Message'] : null,
		'msg' => '获取发送名片返回:sendZhenshiMingpian()',
	);
	writeLog($proxy, $log, "");
	unset($log);
}

/**
 * 新建标签
 * return int 标签id
 */
function createBiaoqian($wxid, $proxy, $name)
{
	
	$url = 'http://'.$proxy.'/api/Label/Add';
	$res = posturl($url, array('LabelName'=>$name, 'Wxid'=>$wxid));
	$labelId = 0;
	if ($res['Code'] == 0 && !empty($res['Data'])) {
		$labelId = $res['Data']['LabelPairList']['labelID'] ?? 0;
	}
	$log = array(
		'Message' => $res['Message'] ?? '',
		'msg' => '创建标签:createBiaoqian()',
		'data'=>$res
	);
	writeLog($proxy, $log, "");
	unset($log);

	return $labelId;
    
    if (empty($res['data']['LabelPairList'][0]['LabelID'])) {
    	return createBiaoqian($guid, $name);
    }
    

}

/**
 * <AUTHOR>
 * @Date   2022-09-17
 * @Desc   添加僵尸粉到标签
 * @Return [return]
 * @param  [string]        $key  [登录人唯一key]
 * @param  [array]        $wxids [需添加人]
 * @param  [string]        $bid [标签id]
 */
function addBiaoqianWxids($wxid, $proxy, $info, $bid)
{

	$url = 'http://'.$proxy.'/api/Label/UpdateList';
	if (!isset($info['UserName']['string'])) return;
	$toWxid = $info['UserName']['string'];
	$params = ['ToWxids'=>$toWxid, 'LabelID'=> "$bid", 'Wxid'=>$wxid];
	$res = posturl($url, $params);
	$log = array(
		'Message' => isset($res['MoveToLabel']) ? $res['MoveToLabel'] : null,
		'params' => $params,
		'data'=>$res,
		'msg' => '添加僵尸粉到标签:addBiaoqianWxids()',
	);
	writeLog($proxy, $log, "");
	unset($log);
	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($wxid, $res, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep(3);
			$res = posturl($url, $params);
		}
	}
	if (!empty($res) && isset($res['Data']['BaseResponse']['ret']) && $res['Data']['BaseResponse']['ret'] != 0) {
		$km = $GLOBALS['km'];
		file_put_contents("/tmp/biaoqianlog/".$km.".log", date("Y/m/d H:i:s")."添加僵尸粉到标签---".var_export($res,true)."\n\n", FILE_APPEND);
		sleep(3);
		$res = posturl($url, $params);
	}
	$log = array(
		'Message' => isset($res['MoveToLabel']) ? $res['MoveToLabel'] : null,
		'params' => $params,
		'data'=>$res,
		'msg' => '添加僵尸粉到标签:addBiaoqianWxids()',
	);
	writeLog($proxy, $log, "");
	unset($log);
	
}


/**
 * <AUTHOR>
 * @Date   2022-09-17
 * @Desc   删除好友
 * @Return [return]
 * @param  [type]        $wxid  [description]
 * @param  [type]        $wxids [description]
 * @return [type]               [description]
 */
function delFriend($wxid, $proxy, $data, $delNum, $blockNum)
{

	$uuid = $GLOBALS['uuid'];
	// 先推送几个
	$delUrl = 'http://'.$proxy.'/api/Friend/Delete';
	$sendText = "";
	foreach ($data as $key => $value) {
		if (empty($value)) continue;
		if (!isset($value['NickName']['string'])) continue;
		$toWxid = $value['UserName']['string'];
		//执行删除操作
		sleep(1);
		$delParams = array(
			'ToWxid' => "$toWxid",
			'Wxid' => $wxid,
		);
		$delRes = posturl($delUrl, $delParams);

		if (!empty($delRes) && isset($delRes['Data']['oplogRet']['errMsg'])) {
			sendTextMsg($wxid, $proxy, $sendText);
			$sendText = "";
			$qingli = $key;
			$weiqingli = count($data)-$key;
			if ($qingli == 0) {
				sendTextMsg($wxid, $proxy, "[成功清理]:".$qingli."（删除频繁）\n[剩余未清理]:".$weiqingli."（删除频繁）\\n请自行检查清理人数是否正确\n由于官方限制，若提示频繁\n默认1小时后自动清理剩余僵尸\n或1小时后再重新登录清理");
			} else {
				sendTextMsg($wxid, $proxy, "[成功清理]:".$qingli."\n[剩余未清理]:".$weiqingli."（删除频繁）\n请自行检查清理人数是否正确\n由于官方限制，若提示频繁\n默认1小时后自动清理剩余僵尸\n或1小时后再重新登录清理");
			}
			file_put_contents("/tmp/shanchu/".$uuid.".log", date("Y/m/d H:i:s")."删除频繁：删除人数：".$qingli.json_encode($delRes, JSON_UNESCAPED_UNICODE)."\n\n", FILE_APPEND);
			writeLog($uuid, ['data'=>$delRes,'params'=>$delParams], "删除频繁");
			for ($i=1; $i < 3600; $i++) { 
				sleep(1);
			}
			$delRes = posturl($delUrl, $delParams);
		}

		$log = array(
			'Message' => isset($delRes['Message']) ? $delRes['Message'] : null,
			'msg' => '删除1好友:delFriend()',
			// 'data'=>$delRes
		);
		writeLog($uuid, $log, "");
		unset($log);
		if (!empty($value['SnsUserInfo']['SnsBgobjectId'])) {
			$sendText = empty($sendText) ? "阿拉丁-已删除[删除我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-已删除[删除我的人]-".$value['NickName']['string'];
		} else {
			$sendText = empty($sendText) ? "阿拉丁-已删除[拉黑我的人]-".$value['NickName']['string'] : $sendText . "\n阿拉丁-已删除[拉黑我的人]-".$value['NickName']['string'];
		}
		if (($key+1) % 10 == 0)  {
			sendTextMsg($wxid, $proxy, $sendText);
			$sendText = "";
		}

	}
	if (!empty($sendText)) sendTextMsg($wxid, $proxy, $sendText);
	unset($data);


}

/**
 * <AUTHOR>
 * @Date   2022-09-17
 * @Desc   删除全部好友
 * @Return [return]
 * @param  [type]        $wxid  [description]
 * @param  [array]        $data [description]
 * @return [type]               [description]
 */
function delAllFriend($wxid, $proxy, $data)
{
	$uuid = $GLOBALS['uuid'];
	// 先推送几个
	$delUrl = 'http://'.$proxy.'/api/Friend/Delete';
	$sendText = "";
	foreach ($data as $key => $value) {
		if (empty($value)) continue;
		$toWxid = $value;
		//执行删除操作
		sleep(1);
		$delParams = array(
			'ToWxid' => "$toWxid",
			'Wxid' => $wxid,
		);
		$delRes = posturl($delUrl, $delParams);
		
		$log = array(
			'msg' => '删除好友:delAllFriend()',
			'params' => $delParams,
			'data'=> $delRes
		);
		writeLog($wxid, $log, "删除好友:delAllFriend()");
		unset($log);

		if (!empty($delRes) && isset($delRes['Data']['oplogRet']['errMsg'])) {
			$qingli = $key;
			$weiqingli = count($data)-$key;
			
			if ($qingli == 0) {
				sendTextMsg($wxid, $proxy, "[成功清理]:".$qingli."（删除频繁）\n[剩余未清理]:".$weiqingli."（删除频繁）\n请自行检查清理人数是否正确\n由于官方限制，若提示频繁\n默认1小时后自动清理剩余僵尸\n或1小时后再重新登录清理");
			} else {
				sendTextMsg($wxid, $proxy, "[成功清理]:".$qingli."\n[剩余未清理]:".$weiqingli."（删除频繁）\\n请自行检查清理人数是否正确\n由于官方限制，若提示频繁\n默认1小时后自动清理剩余僵尸\n或1小时后再重新登录清理");
			}
			
			file_put_contents("/tmp/shanchu/".$uuid.".log", date("Y/m/d H:i:s")."删除频繁：删除人数：".$qingli.json_encode($delRes, JSON_UNESCAPED_UNICODE)."\n\n", FILE_APPEND);
			writeLog($uuid, ['data'=>$delRes,'params'=>$delParams], "删除频繁");
			for ($i=1; $i < 3600; $i++) { 
				sleep(1);
			}
			$delRes = posturl($delUrl, $delParams);
		}
		
		// if (($key+1) % 300 == 0 ) {
		// 	sendTextMsg($wxid, $proxy, "已成功清理300好友，如果提示频繁，1小时后自动执行期间请不要退出");
		// 	// sleep(3600);
		// 	return "";
		// }

		if ($key == 30) sendTextMsg($wxid, $proxy, "清理进度：已删除人数31；");
		if ($key == 60) sendTextMsg($wxid, $proxy, "清理进度：已删除人数61；");
		if ($key == 90) sendTextMsg($wxid, $proxy, "清理进度：已删除人数91；");
		if ($key == 120) sendTextMsg($wxid, $proxy, "清理进度：已删除人数121；");
		if ($key == 150) sendTextMsg($wxid, $proxy, "清理进度：已删除人数151；");
		if ($key == 180) sendTextMsg($wxid, $proxy, "清理进度：已删除人数181；");
		if ($key == 210) sendTextMsg($wxid, $proxy, "清理进度：已删除人数211；");
		if ($key == 240) sendTextMsg($wxid, $proxy, "清理进度：已删除人数241；");
		if ($key == 270) sendTextMsg($wxid, $proxy, "清理进度：已删除人数271；");
		if ($key == 298) sendTextMsg($wxid, $proxy, "清理进度：已删除人数300；");
	}
	unset($data);


}

/**
 * <AUTHOR>
 * @Date   2022-10-03
 * @Desc   获取好友朋友圈
 * @Return [return]
 * @param  [type]        $guid  [description]
 * @param  [type]        $wxids [description]
 */
function WXSnsUserPage($wxid, $proxy, $wxids)
{
	$uuid = $GLOBALS['uuid'];
	$url = 'http://'.$proxy.'/api/FriendCircle/GetDetail';

	$total = count($wxids);
	$rate = 0;
	$allInfos = [];
	foreach ($wxids as $key => $toWxid) {
		$params = array(
			'Towxid' => $toWxid,
			'FirstPageMd5' => "",
			'Maxid' => 0,
			"Wxid" => $wxid
		);

		$res = posturl($url, $params);
		if (isset($res['Code'])&&$res['Code']!=0) {
			if ($res['Code'] == '-13') {
				writeLog($wxid, $res, "检测到退出，停止程序");
				exit("检测到退出，停止程序");
			}
			if ($res['Code'] == '-1' || $res['Code'] == '-8') {
				sleep(3);
				$res = posturl($url, $params);
			}
		}



		if (!isset($res['Data']['ObjectTotalCount'])) continue;

		if ($res['Data']['ObjectTotalCount'][0] == "0") {
			$infos = WXGetContact($wxid, $proxy, [$toWxid]);
			if (empty($infos)) continue;
		    if (isset($GLOBALS['pb_id'])) {
		        addBiaoqianWxids($wxid,$proxy, $infos[0], $pb_id);
		        // WXSetContactRemark($guid, $infos, "屏蔽我的人");
		    } else {
		        //创建标签
		        $pb_id = createBiaoqian($wxid, $proxy, "阿拉丁-屏蔽我的人");
		        
		        addBiaoqianWxids($wxid, $proxy, $infos[0], $pb_id);
		        // WXSetContactRemark($guid, $infos, "屏蔽我的人");
		    }

		    $allInfos[] = $infos[0];
		}


		//计算扫描进度

		$c = $key;
		$rate = rate($wxid, $proxy, $c, $total, $rate, "检测屏蔽扫描进度");
		
	}
	
	return $allInfos;
}


/**
 * <AUTHOR>
 * @Date   2023-07-16
 * @Desc   获取本人朋友圈
 * @Return [return]
 * @param  [type]        $wxid  [description]
 * @param  [type]        $proxy [description]
 * @param  integer       $Maxid [description]
 */
function FriendCircleDEL($wxid, $proxy, $start, $end, $Maxid = 0, $k = 0, $cur=0) {
	$url = 'http://'.$proxy.'/api/FriendCircle/GetDetail';
	$params = array(
		'Towxid' => $wxid,
		'FirstPageMd5' => "",
		'Maxid' => 131230982,
		"Wxid" => $wxid
	);
	$res = posturldel($url, $params, $Maxid);
	sleep(1);

	$log = array(
			'code' => isset($res['Code']) ? $res['Code'] : null,
			'Message' => isset($res['Message']) ? $res['Message'] : null,
			'msg' => '获取朋友圈:FriendCircleDEL()',
			'start'=> '开始时间'.date("Y-m-d H:i:s", $start),
			'end'=> '结束时间'.date("Y-m-d H:i:s", $end),
			'data' => isset($res) ? $res : null
		);
	// writeLog($wxid, $log, "获取朋友圈:WXSnsUserPage()");
	file_put_contents("/tmp/pengyouquan/$wxid.log", date("Y/m/d H:i:s")."获取朋友圈：".json_encode($log, JSON_UNESCAPED_UNICODE)."\n\n", FILE_APPEND);
	unset($log);


	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($wxid, $res, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep(3);
			$res = posturl($url, $params);
			writeLog($proxy, $res, "二次获取朋友圈");
		}
	}

	if (!empty($res) && $res['Code'] == 0 && !empty($res['Data']['ObjectList'])) {
		$data = $res['Data']['ObjectList'] ?? [];


		foreach ($data as $key => $value) {
			// $id = number_format($value['Id'], 0, '', '');
			$id = $value['Id'];

			if ($start > $value['CreateTime']) {
				return $k;
			}

			if ($start < $value['CreateTime'] && $value['CreateTime'] < $end) {
				$cur = 99999;
				$xid = "";
				// if (!empty($value['ObjectDesc']['buffer'])) {
				// 	preg_match('/<id><!\[CDATA\[(.*?)]]><\/id>/i', $value['ObjectDesc']['buffer'], $xid);
				// 	if (empty($xid)) preg_match('/<id>(.*?)<\/id>/i', $value['ObjectDesc']['buffer'], $xid);
				// }
				// //删除朋友圈
				// if (!empty($xid)) {
				// 	$k += 1;
				// 	delFriendCircle($wxid, $proxy, $xid[1], $k);
				// }
				$k += 1;
				delFriendCircle($wxid, $proxy, $id, $k, $value['CreateTime']);
			}

			// if ($value['CreateTime'] > $end) return $k;
		}
		if ($cur != 99999) {
			$cur++;
			if ($cur%4 == 0) sendTextMsg($wxid, $proxy, "加载页数：".$cur);
		}
		return FriendCircleDEL($wxid, $proxy,$start, $end, $id, $k,$cur);
	}
	return $k;
}


/**
 * <AUTHOR>
 * @Date   2023-07-16
 * @Desc   删除朋友圈
 * @Return [return]
 * @param  [type]        $wxid  [description]
 * @param  [type]        $proxy [description]
 * @param  [type]        $id    [description]
 * @return [type]               [description]
 */
function delFriendCircle($wxid, $proxy, $id, $k = 0, $createTime=0) {
	$url = 'http://'.$proxy.'/api/FriendCircle/Operation';
	$params = array(
		'CommnetId' => 0,
		'Id' => $id,
		'Type' => 1,
		"Wxid" => $wxid
	);
	sleep(1);
	$res = posturl($url, $params);
	writeLog($proxy, ['data'=>$res,'delId'=>$id,"createTime"=>$createTime], "清理朋友圈结果返回");

	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($wxid, $res, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep(3);
			$res = posturl($url, $params);
			writeLog($proxy, $res, "二次删除朋友圈结果");
		}
	}

	if (isset($res['Data']['BaseResponse']) && $res['Data']['BaseResponse']['ret'] == '-2') {
		sendTextMsg($wxid, $proxy, "当天清理已达上限\n请明日重新清理");
		die;
	}
	// if ($k % 5 == 0) sendTextMsg($wxid, $proxy, "已删除".$k."条");
	if ($k <= 10) {
		sendTextMsg($wxid, $proxy, "已删除".$k."条");
		return;	
	} if ($k > 10 && $k <=100) {
		if ($k % 5 == 0) sendTextMsg($wxid, $proxy, "已删除".$k."条");
		return;
	} else {
		if ($k % 10 == 0) sendTextMsg($wxid, $proxy, "已删除".$k."条");
		return;
	}
	
}


function writeLog($guid, $data, $title)
{
	// return "";
    $dir = $GLOBALS['dir'];
    $uuid = $GLOBALS['uuid'];
    
    file_put_contents($dir.$uuid.".log", date("Y/m/d H:i:s").$title."：".json_encode($data, JSON_UNESCAPED_UNICODE)."\n\n", FILE_APPEND);
    unset($data);
    unset($guid);
    unset($title);
}

/**
 * <AUTHOR>
 * @Date   2022-09-22
 * @Desc   post请求
 * @Return [return]
 * @param  [type]        $url  [description]
 * @param  array         $data [description]
 * @return [type]              [description]
 */
function posturl($url,$postdata = [], $mix=0){
	$wxid = $postdata['Wxid'] ?? "init_wxid";
	$data = $postdata;
    $postdata = json_encode($postdata);
    # 初始化一个curl会话
    $ch = curl_init();
	# 启用时会发送一个常规的POST请求
    curl_setopt($ch, CURLOPT_POST, 1);
	# 需要获取的URL地址
    curl_setopt($ch, CURLOPT_URL, $url);
	# 全部数据使用HTTP协议中的"POST"操作来发送。
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
	# 将curl_exec()获取的信息以文件流的形式返回，而不是直接输出。
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT,30);   //只需要设置一个秒的数量就可以 
	# 一个用来设置HTTP请求头字段的数组
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            # 设置编码格式
            'Content-Type: application/json;charset=utf-8',
            'Content-Length: ' . strlen($postdata)
        )
   	);
	# curl_exec 执行一个cURL会话。
    $response = curl_exec($ch);
	# 关闭一个cURL会话
    curl_close($ch);

    $resa = json_decode($response, true);


    if (isset($resa['Code'])&&$resa['Code']!=0) {
    	$mix++;
    	writeLog($wxid, ['data'=>$resa,'mix'=>$mix], "返回异常数据");
		if ($resa['Code'] == '-13') {
			writeLog($wxid, $resa, "检测到退出，停止程序");
			exit("检测到退出，停止程序");
		}
		if ($resa['Code'] == '-1' || $resa['Code'] == '-8') {
			file_put_contents("/tmp/response/{$wxid}.log", date("Y/m/d H:i:s")."异常返回"  .var_export($response, true)."\n链接6：".$url."参数：{$postdata}  mix: {$mix}"."\n\n", FILE_APPEND);
			sleep(1);
			if ($mix <= 3 ) {
				if (strpos($resa['Message'], "EOF") !== false) {
					sleep(2);
					return posturl($url, $data, $mix);
				} elseif (strpos($resa['Message'], "数据不存在") !== false) {
					sleep(5);
					return posturl($url, $data, $mix);
				}else {
					sleep(2);
					return posturl($url, $data, $mix);
				}
			}
			
		}
	}
	unset($data);
    return $resa;
}
function posturldel($url,$postdata = [], $Maxid = "0"){
    $postdata = json_encode($postdata);
    $postdata = str_replace('131230982',$Maxid,$postdata);

    # 初始化一个curl会话
    $ch = curl_init();
	# 启用时会发送一个常规的POST请求
    curl_setopt($ch, CURLOPT_POST, 1);
	# 需要获取的URL地址
    curl_setopt($ch, CURLOPT_URL, $url);
	# 全部数据使用HTTP协议中的"POST"操作来发送。
    curl_setopt($ch, CURLOPT_POSTFIELDS, $postdata);
	# 将curl_exec()获取的信息以文件流的形式返回，而不是直接输出。
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT,30);   //只需要设置一个秒的数量就可以 
	# 一个用来设置HTTP请求头字段的数组
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            # 设置编码格式
            'Content-Type: application/json;charset=utf-8',
            'Content-Length: ' . strlen($postdata)
        )
   	);
	# curl_exec 执行一个cURL会话。
    $response = curl_exec($ch);

	# 关闭一个cURL会话
    curl_close($ch);
    return json_decode($response, true, 512, JSON_BIGINT_AS_STRING);
}


function curl_get($url,$data=[]){
    if($url == "" ){
        return false;
    }
    $url = $url.'?'.http_build_query($data);
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true) ;
    curl_setopt($ch, CURLOPT_URL, $url);
    //参数为1表示传输数据，为0表示直接输出显示。
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    //参数为0表示不带头文件，为1表示带头文件
    curl_setopt($ch, CURLOPT_HEADER,0);
    // 关闭SSL验证
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
    $output = curl_exec($ch);
    if(curl_exec($ch) === false){
        echo 'Curl error: ' . curl_error($ch);
    }
    curl_close($ch);
    return $output;
}

function sendLocalSyncMsg($wxid, $proxy, $e_time) {
    $url = 'http://127.0.0.1:10043/likeMoments';
    $params = [
    	'wxid'=>$wxid,
    	'endtime' => intval($e_time),
    	'proxy' => $proxy,
    ];
    return posturl($url, $params);
}


function getFavor($wxid, $proxy, $Keybuf="string", $data=[])
{
	$url = 'http://'.$proxy.'/api/Favor/Sync';
	$params = array(
		'Keybuf' => $Keybuf,
		"Wxid" => $wxid
	);
	$res = posturl($url, $params);
	if (isset($res['Data']['Ret']) && !empty($res['Data']['List'])) {
		$list = [];
		foreach ($res['Data']['List'] as $key => $value) {
			if ($value['Flag'] == 0) $list[] = $value;
		}
		$data = array_merge($data, $list);

		$n = count($data);
		sendTextMsg($wxid, $proxy, "已加载{$n}条收藏");
		sleep(1);
		return getFavor($wxid, $proxy, $res['Data']['KeyBuf']['buffer'], $data);
	}
	return $data;
}

function delFavor($wxid, $proxy, $data, $start, $end) {

	$url = 'http://'.$proxy.'/api/Favor/Del';

	$n = 0;
	foreach ($data as $key => $value) {

		if ($start < $value['UpdateTime'] && $value['UpdateTime'] < $end) {
			$params = [
				'FavId' => $value['FavId'],
				'Wxid' => $wxid
			];

			$res = posturl($url, $params);
			if (!empty($res['Data']) && $res['Data']['BaseResponse']['ret'] ==0) {
				$n++;
				if ($n % 10 == 0) sendTextMsg($wxid, $proxy, "已删除{$n}条收藏");

			}
		}
	}
	return $n;

}
function getLikeList($wxid, $proxy, $e_time) {
	$url = 'http://'.$proxy.'/api/FriendCircle/GetList';
	$params = [
		'Fristpagemd5' => '',
		'Maxid' => 0,
		'Wxid' => $wxid,
	];

	$sleep = 10;
	while (true) {
		if (time() >= $e_time) break;
		$res = posturldel($url, $params);

		if (isset($res['Code'])&&$res['Code']!=0) {
			if ($res['Code'] == '-13') {
				writeLog($proxy, ['wxid'=>$wxid,'data'=>$res], "获取朋友圈首页-检测到退出");
				exit("检测到退出，停止程序");
				break;
			}
			if ($res['Code'] == '-1' || $res['Code'] == '-8') {
				sleep(3);
				$res = posturl($url, $params);
			}
		}
		// writeLog($proxy, $res, "获取朋友圈首页结果返回");

		$objectList = $res['Data']['ObjectList'] ?? [];

		if ($objectList) {
			foreach ($objectList as $key => $value) {
				$id = $value['Id'];
				if (!$value['LikeFlag']) {
					like($wxid, $proxy, $id);
				}
			}
		}
		unset($objectList);
		unset($res);
		sleep($sleep);
	}


	
}

function like($wxid, $proxy, $id) {
	$url = 'http://'.$proxy.'/api/FriendCircle/Comment';
	$params = [
		'Content' => "",
		'Id' => $id,
		'ReplyCommnetId' => 0,
		'Type' => 1,
		'Wxid' => $wxid,
	];
	$res = posturl($url, $params, 5);


	if (isset($res['Code'])&&$res['Code']!=0) {
		if ($res['Code'] == '-13') {
			writeLog($proxy, ['wxid'=>$wxid,'data'=>$res], "点赞朋友圈检测到退出");
			exit("检测到退出，停止程序");
		}
		if ($res['Code'] == '-1' || $res['Code'] == '-8') {
			sleep(3);
			$res = posturl($url, $params,5);
		}
	}
	if (isset($res['Code']) && $res['Code']== "0" && $res['Data']['BaseResponse']['ret'] == "201") {
		sendTextMsg($wxid, $proxy, "由于微信限制，请耐心等待24小时，中间请勿退出ipad，满24小时自动开启");
		sleep(86400);
	}
	if (isset($res['Code']) && $res['Code']== "0" && $res['Data']['BaseResponse']['ret'] == "0") {
		$t = date("Y-m-d H:i:s");
		$nickname = $res['Data']['snsObject']['Nickname'];
		$str = "【朋友圈点赞】功能记录\n {$t} 自动点赞了一条好友【{$nickname}】的朋友圈";
		sendTextMsg($wxid, $proxy, $str);

	}
	// writeLog($proxy, ['data'=>$res,'delId'=>$id], "点赞朋友圈结果返回");
	unset($res);
}




